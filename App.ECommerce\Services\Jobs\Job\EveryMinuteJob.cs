using System;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using AutoMapper;
using log4net;
using Newtonsoft.Json;
using Quartz;
using static App.ECommerce.Resource.Enums.AffiliationEnum;
using MongoDB.Driver;

namespace App.ECommerce.Services.Jobs.Job;

public class EveryMinuteJob : IJob
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(EveryMinuteJob));
    private readonly ICommissionsConfigRepository _commissionsConfigRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IUserRepository _userRepository;
    private readonly ITriggerEventFlow _triggerEventFlow;
    private readonly IZaloZNSFlow _zaloZNSFlow;
    private readonly ITempFilesFlow _tempFilesFlow;
    private readonly IVoucherFlow _voucherFlow;
    private readonly IAffiliateFlow _affiliateFlow;

    public EveryMinuteJob(
        ICommissionsConfigRepository commissionsConfigRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        ITriggerEventFlow triggerEventFlow,
        IZaloZNSFlow zaloZNSFlow,
        ITempFilesFlow tempFilesFlow,
        IVoucherFlow voucherFlow,
        IAffiliateFlow affiliateFlow
    )
    {
        _commissionsConfigRepository = commissionsConfigRepository;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _triggerEventFlow = triggerEventFlow;
        _zaloZNSFlow = zaloZNSFlow;
        _tempFilesFlow = tempFilesFlow;
        _voucherFlow = voucherFlow;
        _affiliateFlow = affiliateFlow;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var requestId = Guid.NewGuid().ToString();
        try
        {
            await _tempFilesFlow.ProcessTempFiles();

            await _voucherFlow.CheckAndExpireVouchersForToday();

            await _affiliateFlow.DeactivateExpiredAffiliationUsers();

            await _affiliateFlow.ProcessCommissionPayments();

            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (env != "Development")
            {
                await _triggerEventFlow.CheckUnpaidOrders();
                await _zaloZNSFlow.GetStatusMessageZNS();
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} Error in EveryMinuteJob: {ex.Message}", ex);
            throw;
        }
    }
}