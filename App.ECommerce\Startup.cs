﻿using App.Base;
using App.Base.Converter;
using App.Base.Middleware;
using App.Base.Repository;
using App.Base.Repository.Interface;
using App.ECommerce.Helpers;
using App.ECommerce.Languages.LangAPI;
using App.ECommerce.Middleware;
using App.ECommerce.ProcessFlow;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Implement;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services;
using App.ECommerce.Services.Transport;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Log4Net;
using AutoMapper;
using BackgroundQueue;
using BackgroundQueue.Generic;
using Microsoft.AspNetCore.CookiePolicy;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption;
using Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.Extensions.Localization;
using Minio;
using System.Globalization;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using App.ECommerce.Services.Jobs;
using App.ECommerce.Services.Jobs.Job;
using Quartz;
using Quartz.Impl;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Helpers.Implement;
using App.ECommerce.ProcessFlow.Implement;
using App.ECommerce.ProcessFlow.Interface;
using System.Net.Http;

namespace App.ECommerce;

public class Startup
{
    private IConfiguration Configuration { get; }

    public Startup(IConfiguration configuration)
    {
        this.Configuration = configuration;
    }

    // This method gets called by the runtime. Use this method to add serices to the container.
    public void ConfigureService(IServiceCollection services, string enviromentValue)
    {
        string domainWildcard = this.Configuration["ApplicationSettings:DomainWildcard"] ?? @"localhost";
        Constants.AppName = "ECommerce";
        Constants.AppDomain = domainWildcard == "" ? $"localhost" : domainWildcard;
        Constants.AppUrl = domainWildcard == "" ? $"http://localhost:5077" : $"https://{domainWildcard}";
        Constants.AppSocketUrl = domainWildcard == "" ? $"ws://localhost:5077" : $"wss://{domainWildcard}";
        Constants.AppSignalUrl = domainWildcard == "" ? $"http://localhost:5077" : $"https://{domainWildcard}";
        Constants.CookieDomain = domainWildcard == "" ? $"" : $".{domainWildcard}";
        Constants.CookieSecure = domainWildcard != "";
        Logs.debug($"CookieSameSite={Constants.CookieSameSite}");
        Logs.debug($"CookieHttpOnly={Constants.CookieHttpOnly}");
        Logs.debug($"CookieDomain={Constants.CookieDomain}");
        Logs.debug($"CookieSecure={Constants.CookieSecure}");
        Logs.debug($"SecurePolicy={(string.IsNullOrEmpty(Constants.CookieDomain) ? CookieSecurePolicy.None.ToString() : CookieSecurePolicy.Always.ToString())}");

        services.AddBase(Configuration, enviromentValue);

        // Configuring Languages-Pages
        services.AddLocalization(o => { o.ResourcesPath = "Languages/LangPage"; });
        services.Configure<RequestLocalizationOptions>(options =>
        {
            options.SetDefaultCulture("vi-VN");
            options.AddSupportedUICultures("en-US", "vi-VN");
            options.FallBackToParentUICultures = true;
            options.RequestCultureProviders.Clear();
        });
        services
            .AddControllersWithViews()
            .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix)
            .AddDataAnnotationsLocalization();

        // Configuring Firebase
        services.AddSingleton<FirebaseMessagingService>();

        // Configuring Controller & RazorPage
        services
            .AddRazorPages()
            .AddRazorPagesOptions(options =>
            {
                options.Conventions.AuthorizeAreaFolder("admin", "/dashboard");
                options.Conventions.AuthorizeAreaPage("admin", "/user", "/logout");
                options.Conventions.AddAreaPageRoute("admin", "/user", "/login");
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterDateTimeNullable());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterEnumNullable());
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
                options.SerializerSettings.PreserveReferencesHandling = Newtonsoft.Json.PreserveReferencesHandling.None;
                options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.None;
                options.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter>
                {
                    new Newtonsoft.Json.Converters.StringEnumConverter(),
                    new NewtonsoftJsonConverterDateTimeNullable(),
                    new NewtonsoftJsonConverterEnumNullable(),
                };
                options.SerializerSettings.DateFormatString = "yyyy-MM-ddTHH:mm:ss";
                options.SerializerSettings.Culture = new CultureInfo("vi-VN"); //vi-VN en-US
            })
            .AddRazorRuntimeCompilation()
            .AddSessionStateTempDataProvider();
        services
            .AddControllersWithViews(options => { options.EnableEndpointRouting = false; })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterDateTimeNullable());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterEnumNullable());
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
                options.SerializerSettings.PreserveReferencesHandling = Newtonsoft.Json.PreserveReferencesHandling.None;
                options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.None;
                options.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter>
                {
                    new Newtonsoft.Json.Converters.StringEnumConverter(),
                    new NewtonsoftJsonConverterDateTimeNullable(),
                    new NewtonsoftJsonConverterEnumNullable(),
                };
                options.SerializerSettings.DateFormatString = "yyyy-MM-ddTHH:mm:ss";
                options.SerializerSettings.Culture = new CultureInfo("vi-VN"); //vi-VN en-US
            })
            .AddSessionStateTempDataProvider();
        services
            .AddMvc(options => { options.EnableEndpointRouting = false; })
            .ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = context =>
                {
                    var problems = new CustomBadRequest(context);
                    return new BadRequestObjectResult(problems);
                };
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterDateTimeNullable());
                options.JsonSerializerOptions.Converters.Add(new JsonConverterEnumNullable());
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
                options.SerializerSettings.PreserveReferencesHandling = Newtonsoft.Json.PreserveReferencesHandling.None;
                options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.None;
                options.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter>
                {
                    new Newtonsoft.Json.Converters.StringEnumConverter(),
                    new NewtonsoftJsonConverterDateTimeNullable(),
                    new NewtonsoftJsonConverterEnumNullable(),
                };
                options.SerializerSettings.DateFormatString = "yyyy-MM-ddTHH:mm:ss";
                options.SerializerSettings.Culture = new CultureInfo("vi-VN"); //vi-VN en-US
            })
            .AddSessionStateTempDataProvider();

        services.AddRouting(options => options.LowercaseUrls = true);
        services.AddHttpContextAccessor();
        services.AddMemoryCache();
        services.AddDistributedMemoryCache();

        services.AddAntiforgery();
        services
            .AddDataProtection()
            .UseCryptographicAlgorithms(new AuthenticatedEncryptorConfiguration()
            {
                EncryptionAlgorithm = EncryptionAlgorithm.AES_256_CBC,
                ValidationAlgorithm = ValidationAlgorithm.HMACSHA256
            })
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90))
            .SetApplicationName("SharedCookieApp");
        //.PersistKeysToFileSystem(new System.IO.DirectoryInfo(@"c:\PATH TO COMMON KEY RING FOLDER"))

        services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromDays(1);
            options.Cookie.Name = ".Sys.Session";
            options.Cookie.IsEssential = true;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = string.IsNullOrEmpty(Constants.CookieDomain) ? CookieSecurePolicy.None : CookieSecurePolicy.Always;
            options.Cookie.Domain = Constants.CookieDomain;
        });
        services.Configure<CookiePolicyOptions>(options =>
        {
            options.CheckConsentNeeded = (_) => false;
            options.MinimumSameSitePolicy = SameSiteMode.None;
            options.HttpOnly = HttpOnlyPolicy.Always;
            options.Secure = CookieSecurePolicy.Always;
        });
        services.ConfigureApplicationCookie(options =>
        {
            options.LoginPath = new PathString("/admin/user/login");
            options.AccessDeniedPath = new PathString("/admin/user/accessdenied");
            options.LogoutPath = new PathString("/admin/user/logout");
            options.Cookie = new CookieBuilder
            {
                Name = ".Sys.Cookie",
                SameSite = SameSiteMode.Lax,
                HttpOnly = true,
                SecurePolicy = string.IsNullOrEmpty(Constants.CookieDomain) ? CookieSecurePolicy.None : CookieSecurePolicy.Always,
                Domain = Constants.CookieDomain,
            };
            options.SlidingExpiration = true;
            options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
        });

        services.AddCors(options =>
        {
            options.AddPolicy("AllowSavedDomain", builder =>
                {
                    builder.SetIsOriginAllowed(origin =>
                        {
                            try
                            {
                                var uri = new Uri(origin);
                                return uri.Host.StartsWith("192.168.");
                            }
                            catch
                            {
                                return false;
                            }
                        })
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
        });

        // Add a middleware to dynamically update CORS policy with domains from DomainName table
        services.AddTransient<IStartupFilter, CorsStartupFilter>();

        services.Configure<ForwardedHeadersOptions>(options => { options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.All; });

        // === Authentication for api
        // === Authentication for web

        // === Authorization
        services.AddAuthorization(options =>
        {
            options.AddBase();
            // For api
            options.AddPolicy(PolicyPrefix.RequiresUser, policy => { policy.RequireClaim(JwtClaimNames.Prefix_Role, RolePrefix.User); });
            options.AddPolicy(PolicyPrefix.RequiresMember, policy => { policy.RequireClaim(JwtClaimNames.Prefix_Role, RolePrefix.Member); });
            options.AddPolicy(PolicyPrefix.RequiresPartner, policy => { policy.RequireClaim(JwtClaimNames.Prefix_Role, RolePrefix.Partner); });
            // For Two-factor authentication (2FA)
            //options.AddPolicy("TwoFactorEnabled", policy => { policy.RequireClaim("amr", "mfa"); });
        });

        // Configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        services.AddSwaggerGen(Configuration);
        services.AddEndpointsApiExplorer();

        // Configuring AutoMapper
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MappingProfile>();
            cfg.AddProfile<CommissionsConfigMapping>();
            cfg.AddProfile<AffiliationPartnerMapping>();
            cfg.AddProfile<RecruitmentPageMapping>();
            cfg.AddMaps(typeof(Startup).Assembly);
        });

        IMapper mapper = config.CreateMapper();
        services.AddSingleton(mapper);

        // Configuring Queue
        services.AddBackgroundTaskQueue();
        services.AddBackgroundResultQueue();
        services.AddHostedService<StartupService>();

        // Add Quartz services
        services.AddQuartz(q =>
        {
            q.UseMicrosoftDependencyInjectionJobFactory();

            var jobKey = new JobKey("JobStartupService");
            q.AddJob<JobStartupService>(opts => opts.WithIdentity(jobKey));
            q.AddTrigger(opts => opts
                .ForJob(jobKey)
                .WithIdentity("JobStartupService-trigger")
                .StartNow());
        });
        services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

        // Đăng ký Quartz
        services.AddQuartz(q =>
        {
            // CreateVoucherDetailsJob (không cần trigger định kỳ, chỉ đăng ký job type để enqueue runtime)
            q.AddJob<CreateVoucherDetailsJob>(opts => opts.StoreDurably());

            // Comment out SyncNhanhProductsJob và SyncNhanhOrdersJob
            // var syncNhanhProductsJobKey = new JobKey("SyncNhanhProductsJob");
            // q.AddJob<SyncNhanhProductsJob>(opts => opts.WithIdentity(syncNhanhProductsJobKey));

            // string syncNhanhCronExpression = "0 13 13 * * ?"; // Chạy mỗi tiếng 1 lần (phút 0 của mỗi giờ)
            // q.AddTrigger(opts => opts
            //     .ForJob(syncNhanhProductsJobKey)
            //     .WithIdentity("SyncNhanhProductsJobTrigger")
            //     .WithCronSchedule(syncNhanhCronExpression));

            // var syncNhanhOrdersJobKey = new JobKey("SyncNhanhOrdersJob");
            // q.AddJob<SyncNhanhOrdersJob>(opts => opts.WithIdentity(syncNhanhOrdersJobKey));

            // // Tạo Trigger chạy mỗi 10 phút
            // string syncNhanhOrdersCronExpression = "0 45 12 * * ?"; // Chạy mỗi tiếng 1 lần (phút 30 của mỗi giờ)
            // q.AddTrigger(opts => opts
            //     .ForJob(syncNhanhOrdersJobKey)
            //     .WithIdentity("SyncNhanhOrdersJobTrigger")
            //     .WithCronSchedule(syncNhanhOrdersCronExpression));
        });
        services.AddTransient<OrderTools>();
        // Đăng ký Quartz Hosted Service
        services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

        // Đăng ký service cho background job tạo voucher details
        services.AddTransient<IVoucherJobService, VoucherJobService>();
        services.AddTransient<CreateVoucherDetailsJob>();

        // Inject Data
        services.Configure<ApplicationSettings>(this.Configuration.GetSection("ApplicationSettings"));
        services.Configure<S3Options>(this.Configuration.GetSection("S3Options"));

        // Configuring S3
        bool isUseS3 = bool.Parse(!string.IsNullOrEmpty(this.Configuration["S3Options:IsUseS3"]) ? this.Configuration["S3Options:IsUseS3"] : "false");
        if (isUseS3)
        {
            services.AddMinio(configureClient => configureClient
                .WithEndpoint(this.Configuration["S3Options:Endpoint"])
                .WithCredentials(this.Configuration["S3Options:AccessKey"], this.Configuration["S3Options:SecretKey"])
                .WithSSL(bool.Parse(!string.IsNullOrEmpty(this.Configuration["S3Options:UseHttps"]) ? this.Configuration["S3Options:UseHttps"] : "false"))
                .Build());
        }

        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IStringLocalizer, JsonStringLocalizer>();
        services.AddSingleton<ILogManager, LogManager>();
        services.AddSingleton<HttpClient>();

        services.AddTransient<EventLogsService>();

        // Add more services to the container.
        services.AddSingleton<IConfigurationsRepository<SettingSys>, ConfigurationsRepository<SettingSys>>();
        services.AddScoped<IPartnerRepository, PartnerRepository>();
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IOtpRepository, OtpRepository>();
        services.AddScoped<IShopRepository, ShopRepository>();
        services.AddScoped<IArticleCategoryRepository, ArticleCategoryRepository>();
        services.AddScoped<IArticleRepository, ArticleRepository>();
        services.AddScoped<ICategoryRepository, CategoryRepository>();
        services.AddScoped<IAdvertiseRepository, AdvertiseRepository>();
        services.AddScoped<IBranchRepository, BranchRepository>();
        services.AddScoped<IDashboardRepository, DashboardRepository>();
        services.AddScoped<IGroupFileRepository, GroupFileRepository>();
        services.AddScoped<IItemsRepository, ItemsRepository>();
        services.AddScoped<IWarehouseRepository, WarehouseRepository>();
        services.AddScoped<IVoucherRepository, VoucherRepository>();
        services.AddScoped<ISettingRepository, SettingRepository>();
        services.AddScoped<ICartRepository, CartRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<IShippingAddressRepository, ShippingAddressRepository>();
        services.AddScoped<ITagRepository, TagRepository>();
        services.AddScoped<IDomainNameRepository, DomainNameRepository>();
        services.AddScoped<IShopMiniAppRepository, ShopMiniAppRepository>();
        services.AddScoped<IItemOptionGroupRepository, ItemOptionGroupRepository>();
        services.AddScoped<IItemOptionRepository, ItemOptionRepository>();
        services.AddScoped<IOrderToolsV2, OrderToolsV2>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();
        services.AddScoped<IShopSettingRepository, ShopSettingRepository>();
        services.AddScoped<IZalo_Template_Repository, Zalo_Template_Repository>();
        services.AddScoped<ITransportService, TransportService>();
        services.AddScoped<IRequestLogRepository, RequestLogRepository>();
        services.AddScoped<IMembershipLevelRepository, MembershipLevelRepository>();
        services.AddScoped<ICalcMemberLevelFlow, CalcMemberLevelFlow>();
        services.AddScoped<IZalo_Button_Repository, Zalo_Button_Repository>();
        services.AddScoped<ITriggerEventRepository, TriggerEventRepository>();
        services.AddScoped<IZaloRepository, ZaloRepository>();
        services.AddScoped<IZalo_Authorization_HistoryRepository, Zalo_Authorization_HistoryRepository>();
        services.AddScoped<IQuartzJobRepository, QuartzJobRepository>();
        services.AddScoped<IQuartzJobManagerRepository, QuartzJobManagerRepository>();
        services.AddScoped<IZalo_Message_Type_Repository, Zalo_Message_Type_Repository>();
        services.AddScoped<IZalo_Send_Type_Repository, Zalo_Send_Type_Repository>();
        services.AddScoped<IZalo_WebhookRepository, Zalo_WebhookRepository>();
        services.AddScoped<IZalo_TypeMessageRepository, Zalo_TypeMessageRepository>();
        services.AddScoped<IPartner_Balance_LogRepository, Partner_Balance_LogRepository>();
        services.AddScoped<ICryptoRepository, CryptoRepository>();
        services.AddScoped<ICommissionsConfigRepository, CommissionsConfigRepository>();
        services.AddScoped<IAffiliationPartnerRepository, AffiliationPartnerRepository>();
        services.AddScoped<ICommissionOrderFlow, CommissionOrderFlow>();
        services.AddScoped<IShopVisitRepository, ShopVisitRepository>();
        services.AddScoped<IRecruitmentPageRepository, RecruitmentPageRepository>();
        services.AddScoped<ICommissionsReportRepository, CommissionsReportRepository>();
        services.AddScoped<ICommissionsReportFlow, CommissionsReportFlow>();
        services.AddScoped<IAffiliateFlow, AffiliateFlow>();
        services.AddScoped<ICommissionsConfigHelpers, CommissionsConfigHelpers>();
        services.AddScoped<IUserOverviewRepository, UserOverviewRepository>();
        services.AddScoped<IUserGroupRepository, UserGroupRepository>();
        services.AddScoped<ITransportMethodRepository, TransportMethodRepository>();
        services.AddScoped<ISepayTransactionRepository, SepayTransactionRepository>();
        services.AddScoped<IStorageRepository, StorageRepository>();
        services.AddScoped<IEventLogRepository, EventLogRepository>();
        services.AddScoped<ICommissionsCalculatorHeplers, CommissionsCalculatorHeplers>();
        services.AddScoped<IPartnerFunctionRepository, PartnerFunctionRepository>();
        services.AddScoped<IPartnerRoleRepository, PartnerRoleRepository>();
        services.AddScoped<IZaloZNSRepository, ZaloZNSRepository>();
        services.AddScoped<ICampaignRepository, CampaignRepository>();
        services.AddScoped<IFilterConditionRepository, FilterConditionRepository>();
        services.AddScoped<ITriggerEventRepository, TriggerEventRepository>();
        services.AddScoped<ITriggerEventHistoryRepository, TriggerEventHistoryRepository>();
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<IInvoiceConfigRepository, InvoiceConfigRepository>();
        services.AddScoped<IInvoiceHistoryRepository, InvoiceHistoryRepository>();
        services.AddScoped<IViettelInvoiceRepository, ViettelInvoiceRepository>();
        services.AddScoped<ITaxInvoiceConfigurationRepository, TaxInvoiceConfigurationRepository>();
        services.AddScoped<IInvoiceFlow, InvoiceFlow>();
        services.AddScoped<IPointVoucherCodeRepository, PointVoucherCodeRepository>();
        services.AddScoped<IFunctionRepository, FunctionRepository>();
        services.AddScoped<IPackageRepository, PackageRepository>();
        services.AddScoped<IOrderFlow, OrderFlow>();
        services.AddScoped<IOrderValidator, OrderValidator>();
        services.AddScoped<IOrderCalculator, OrderCalculator>();
        services.AddScoped<IZaloUIDFlow, ZaloUIDFlow>();
        services.AddScoped<IZaloZNSFlow, ZaloZNSFlow>();
        services.AddScoped<ITriggerEventFlow, TriggerEventFlow>();
        services.AddScoped<ICalcMemberLevelFlow, CalcMemberLevelFlow>();
        services.AddScoped<IMembershipLevelFlow, MembershipLevelFlow>();

        // Gamification Services
        services.AddScoped<IGamificationRepository, GamificationRepository>();
        services.AddScoped<IGamificationFlow, GamificationFlow>();
        services.AddScoped<IPromogameEventRepository, PromogameEventRepository>();
        services.AddScoped<IGamificationShopRepository, GamificationShopRepository>();

        services.AddScoped<IVoucherFlow, VoucherFlow>();
        services.AddScoped<IEmailRepository, EmailRepository>();
        services.AddScoped<ITempFilesRepository, TempFilesRepository>();
        services.AddScoped<ITempFilesFlow, TempFilesFlow>();
        services.AddScoped<IBusinessTypeRepository, BusinessTypeRepository>();
        services.AddScoped<IPriceListRepository, PriceListRepository>();
        services.AddScoped<IPriceListFlow, PriceListFlow>();
        services.AddScoped<IBaseFlow, BaseFlow>();
        services.AddScoped<ISyncServiceFlow, SyncServiceFlow>();
        services.AddScoped<ISyncServiceConfigRepository, SyncServiceConfigRepository>();
        services.AddScoped<ISyncServiceHelper, SyncServiceHelper>();
        services.AddScoped<INhanhHelper, NhanhHelper>();
        services.AddScoped<ISapoHelper, SapoHelper>();
        services.AddScoped<IKiotVietHelper, KiotVietHelper>();
        services.AddScoped<IOdooHelper, OdooHelper>();
        services.AddScoped<ICartFlow, CartFlow>();
        services.AddScoped<IAdvertiseFlow, AdvertiseFlow>();
        services.AddScoped<IArticleFlow, ArticleFlow>();
        services.AddScoped<IShopFlow, ShopFlow>();
        services.AddScoped<IItemsFlow, ItemsFlow>();
        services.AddScoped<ICategoryFlow, CategoryFlow>();
        services.AddScoped<IUserFlow, UserFlow>();
        services.AddScoped<IBranchFlow, BranchFlow>();
        services.AddScoped<IPrizeRepository, PrizeRepository>();
        services.AddScoped<IZaloMiniAppRepository, ZaloMiniAppRepository>();
    }
    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public async Task ConfigureApp(WebApplication app, IWebHostEnvironment env, string enviromentValue)
    {
        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Error");
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }
        else
        {
            app.UseDeveloperExceptionPage();
        }

        var serviceScopeFactory = app.Services.GetRequiredService<IServiceScopeFactory>();
        var serviceProvider = serviceScopeFactory.CreateScope().ServiceProvider;
        await app.AddBase(Configuration, serviceProvider, enviromentValue);

        // app.UseStatusCodePages(context =>
        // {
        //     var request = context.HttpContext.Request;
        //     var response = context.HttpContext.Response;
        //     if ((request.Path.ToString().IndexOf("/api/", StringComparison.Ordinal) < 0))
        //     {
        //         if (response.StatusCode == (int)HttpStatusCode.Unauthorized)
        //         {
        //             response.Redirect($"/admin/user/login");
        //         }
        //         else if (response.StatusCode == (int)HttpStatusCode.NotFound)
        //         {
        //             response.Redirect($"/admin/extras/404");
        //         }
        //         else if (response.StatusCode == (int)HttpStatusCode.InternalServerError)
        //         {
        //             response.Redirect($"/admin/extras/500");
        //         }
        //         else if (response.StatusCode == (int)HttpStatusCode.Forbidden)
        //         {
        //             response.Redirect($"/admin/extras/403");
        //         }
        //         else
        //         {
        //             response.Redirect($"/admin/extras/500?code={response.StatusCode}");
        //         }
        //     }

        //     return Task.CompletedTask;
        // });
        app.UseForwardedHeaders(new ForwardedHeadersOptions
        {
            ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.All
        });

        // Configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        app.AddSwaggerGen(Configuration);

        // Add Languages-Pages
        IList<CultureInfo> supportedCultures = new List<CultureInfo>
        {
            new CultureInfo("en-US"),
            new CultureInfo("vi-VN"),
        };
        var localizationOptions = new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture(culture: "vi-VN", uiCulture: "vi-VN"),
            SupportedCultures = supportedCultures,
            SupportedUICultures = supportedCultures
        };
        var cookieProvider = localizationOptions.RequestCultureProviders.OfType<CookieRequestCultureProvider>().First();
        cookieProvider.CookieName = ".MyLanguage";
        app.UseRequestLocalization(localizationOptions);

        // Add Languages-API
        app.Use((context, next) =>
        {
            string userLangs = context.Request.Query["lang"].ToString();
            if (string.IsNullOrEmpty(userLangs)) userLangs = context.Request.Headers["Accept-Language"].ToString();
            var firstLang = userLangs.Split(',').FirstOrDefault();
            string lang;
            switch (firstLang)
            {
                case "vi":
                case "vi-VN":
                    lang = "vi";
                    break;
                case "en":
                case "en-US":
                    lang = "en";
                    break;
                default:
                    lang = "en";
                    break;
            }
            Thread.CurrentThread.CurrentCulture = new CultureInfo(lang);
            context.Items["deviceLanguage"] = lang;
            return next();
        });

        // Apply CORS early in the pipeline
        app.UseCors("AllowSavedDomain");
        app.UseHttpsRedirection();
        app.UseStaticFiles();

        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();
        app.UseSession();
        if (!string.IsNullOrEmpty(Constants.CookieDomain))
        {
            app.UseCookiePolicy();
        }
        app.MapRazorPages();
        // required remove @Page index
        app.MapControllerRoute(name: "areas", pattern: "{area:exists}/{controller}/{action}/{id?}");
        app.MapControllerRoute(name: "default", pattern: "{controller=System}/{action=Login}/{id?}");
        app.UseMvc(routes => { routes.MapRoute(name: "default", template: "{controller=System}/{action=Login}/{id?}"); });
    }
}
