using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Resource.Model;
using System.Globalization;
using Microsoft.Extensions.Localization;
using log4net;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;

namespace App.ECommerce.Controllers.API
{
    [ApiController]
    [Route("/hooks/sepay-payment")]
    public class SepayTransactionController : BaseController
    {
        private readonly ILog _log = log4net.LogManager.GetLogger(typeof(SepayTransactionController));
        private readonly ISepayTransactionRepository _repository;
        private readonly IStringLocalizer _localizer;
        private readonly ICartRepository _cartRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IPartnerRepository _partnerRepository;
        private readonly IShopRepository _shopRepository;
        private readonly IItemsRepository _itemsRepository;
        private readonly IUserRepository _userRepository;
        private readonly IInvoiceFlow _invoiceFlow;
        private readonly IPartner_Balance_LogRepository _partnerBalanceLogRepository;

        public SepayTransactionController(
            ISepayTransactionRepository repository,
            IStringLocalizer localizer,
            ICartRepository cartRepository,
            IOrderRepository orderRepository,
            IPartnerRepository partnerRepository,
            IShopRepository shopRepository,
            IItemsRepository itemsRepository,
            IUserRepository userRepository,
            IInvoiceFlow invoiceFlow,
            IPartner_Balance_LogRepository partnerBalanceLogRepository
        ) : base(localizer)
        {
            _repository = repository;
            _localizer = localizer;
            _cartRepository = cartRepository;
            _orderRepository = orderRepository;
            _partnerRepository = partnerRepository;
            _shopRepository = shopRepository;
            _itemsRepository = itemsRepository;
            _userRepository = userRepository;
            _invoiceFlow = invoiceFlow;
            _partnerBalanceLogRepository = partnerBalanceLogRepository;
        }

        [HttpPost]
        public async Task<IActionResult> Receive([FromBody] SepayTransactionRequest data)
        {
            LogEvent(new EventLogDto
            {
                RefId = data.account_number,
                RequestId = Request.Path.ToString(),
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = "https://dev-admin.evotech.vn/hooks/sepay-payment",
                Message = "Receive webhook from Sepay",
                RefType = TypeFor.Other,
                DataObject = data
            });

            if (data == null)
                return BadRequest(new { success = false, message = "No data" });

            var entity = new SepayTransaction
            {
                gateway = data.gateway,
                transaction_date = data.transaction_date,
                account_number = data.account_number,
                bank_account_id = data.bank_account_id,
                va = data.va,
                payment_code = data.payment_code,
                transfer_type = data.transfer_type,
                amount = data.amount,
                accumulated = data.accumulated,
                content = data.content,
                reference_code = data.reference_code,
                transaction_id = data.transaction_id,
                CreatedAt = DateTime.UtcNow
            };
            await _repository.CreateAsync(entity);
            Console.WriteLine($"[SePay] Receive webhook: {System.Text.Json.JsonSerializer.Serialize(data)}");
            if (string.IsNullOrEmpty(data.content))
                return BadRequest(new { success = false, message = "No data" });

            var match = System.Text.RegularExpressions.Regex.Match(data.content, @"[A-Z0-9]{13}");
            if (!match.Success)
                return Ok(new { success = false, message = "No cartNo found in content" });
            var cartNo = match.Value;
            var orderNo = match.Value;

            var cart = _cartRepository.FindCartByCartNo(cartNo);
            var order = _orderRepository.FindByOrderNo(orderNo);
            bool updated = false;

            if (cart != null)
            {
                if (data.amount < cart.Price)
                {
                    Console.WriteLine("[SePay] Transfer amount not enough for cart");
                    return Ok(new { success = false, message = "Transfer amount not enough for cart" });
                }
                var partner = await _partnerRepository.FindByPartnerId(cart.PartnerId);
                var shop = _shopRepository.FindByShopId(cart.ShopId);
                if (partner == null || shop == null || shop.PartnerId != partner.PartnerId)
                {
                    Console.WriteLine("[SePay] Partner or shop invalid");
                    return Ok(new { success = false, message = "Partner or shop invalid" });
                }
                decimal transactionFee = 450;
                if (partner.Balance < transactionFee)
                {
                    return BadRequest(new { success = false, message = _localizer["INSUFFICIENT_BALANCE"] });
                }
                var updatedPartner = _partnerRepository.UpdatePartnerBalance(partner.PartnerId, partner.Balance - transactionFee);
                var balanceLog = new Partner_Balance_Log
                {
                    PartnerId = partner.PartnerId,
                    RefType = PartnerBalanceRefEnum.SepayFee,
                    RefId = order.OrderId,
                    Type = PartnerBalanceEnum.Deduction,
                    Amount = transactionFee,
                    BalanceAfterTransaction = updatedPartner.Balance,
                    Message = $"Phí giao dịch sepay cho đơn hàng: {order.OrderNo}",
                    Status = PartnerBalanceStatus.Success
                };

                await _partnerBalanceLogRepository.Create(balanceLog);

                cart.ListItems ??= new List<ItemsOrder>();
                var checkItemsList = _itemsRepository.FindByItemsIds(string.Join(",", cart.ListItems.Select(x => x.ItemsId).Distinct()));
                if (cart.ListItems.Count != checkItemsList.Count)
                {
                    Console.WriteLine("[SePay] Cart items invalid");
                    return Ok(new { success = false, message = "Cart items invalid" });
                }
                foreach (var item in cart.ListItems)
                {
                    var check = checkItemsList.FirstOrDefault(x => x.ItemsId == item.ItemsId);
                    if (check == null || check.Quantity <= 0) continue;
                    item.ItemsCode = check.ItemsCode;
                    item.PartnerId = check.PartnerId;
                    item.ShopId = check.ShopId;
                    item.ItemsType = check.ItemsType;
                    item.CategoryIds = check.CategoryIds;
                    item.ItemsName = check.ItemsName;
                    item.Images = check.Images;
                    item.Price = check.Price;
                }
                var user = _userRepository.FindByUserId(cart.UserId ?? "");
                var creator = user == null ? null : new ShippingAddress
                {
                    FullName = user.Fullname,
                    PhoneNumber = user.PhoneNumber,
                    ProvinceId = user.ProvinceId,
                    ProvinceName = user.ProvinceName,
                    DistrictId = user.DistrictId,
                    DistrictName = user.DistrictName,
                    WardId = user.WardId,
                    WardName = user.WardName,
                    Address = user.Address,
                    UserId = user.UserId,
                    Id = user.Id
                };
                var newOrder = new Order
                {
                    OrderId = Guid.NewGuid().ToString(),
                    TransactionId = Guid.NewGuid().ToString(),
                    PartnerId = cart.PartnerId,
                    ShopId = cart.ShopId,
                    Creator = creator,
                    ListItems = cart.ListItems,
                    Price = cart.Price,
                    StatusPay = TypePayStatus.Paid,
                    StatusOrder = TypeOrderStatus.Success,
                    Status = TypeStatus.Actived,
                    Created = DateTime.UtcNow,
                    Updated = DateTime.UtcNow
                };
                await _orderRepository.CreateOrder(newOrder);
                _cartRepository.DeleteCart(cart.CartId);
                updated = true;
            }
            else if (order != null)
            {
                if (string.IsNullOrEmpty(order.PartnerId) && !string.IsNullOrEmpty(order.ShopId))
                {
                    var shop = _shopRepository.FindByShopId(order.ShopId);
                    if (shop != null && !string.IsNullOrEmpty(shop.PartnerId))
                    {
                        order.PartnerId = shop.PartnerId;
                        _orderRepository.UpdateOrder(order);
                    }
                }

                var partner = await _partnerRepository.FindByPartnerId(order.PartnerId);

                if (data.amount < order.Price)
                {
                    Console.WriteLine("[SePay] Transfer amount not enough for order");
                    return Ok(new { success = false, message = "Transfer amount not enough for order" });
                }
                if (string.IsNullOrEmpty(order.PartnerId) && !string.IsNullOrEmpty(order.ShopId))
                {
                    var shop = _shopRepository.FindByShopId(order.ShopId);
                    if (shop != null && !string.IsNullOrEmpty(shop.PartnerId))
                    {
                        order.PartnerId = shop.PartnerId;
                        _orderRepository.UpdateOrder(order);
                    }
                }
                if (order.StatusPay != TypePayStatus.Paid || order.StatusOrder != TypeOrderStatus.Success)
                {
                    order.StatusPay = TypePayStatus.Paid;
                    order.StatusOrder = TypeOrderStatus.Success;
                    order.Updated = DateTime.UtcNow;
                    _orderRepository.UpdateOrder(order);
                }

                if (partner != null)
                {
                    decimal transactionFee = 450;
                    if (partner.Balance < transactionFee)
                    {
                        return BadRequest(new { success = false, message = _localizer["INSUFFICIENT_BALANCE"] });
                    }
                    var updatedPartner = _partnerRepository.UpdatePartnerBalance(partner.PartnerId, partner.Balance - transactionFee);
                    var balanceLog = new Partner_Balance_Log
                    {
                        PartnerId = partner.PartnerId,
                        RefType = PartnerBalanceRefEnum.SepayFee,
                        RefId = order.OrderId,
                        Type = PartnerBalanceEnum.Deduction,
                        Amount = transactionFee,
                        BalanceAfterTransaction = updatedPartner.Balance,
                        Message = $"Phí giao dịch sepay cho đơn hàng: {order.OrderNo}",
                        Status = PartnerBalanceStatus.Success
                    };
                    await _partnerBalanceLogRepository.Create(balanceLog);
                }

                updated = true;
            }
            if (!updated)
            {
                Console.WriteLine("[SePay] Cart/Order not found");
                return Ok(new { success = false, message = "Cart/Order not found" });
            }

            if (order != null && !string.IsNullOrEmpty(order.OrderId))
            {
                await _invoiceFlow.IssueInvoiceAsync($"Webhook_Sepay_{order.OrderId}", order.OrderId);
            }

            return Ok(new { success = true, message = "Cart/Order updated" });
        }
    }
}
