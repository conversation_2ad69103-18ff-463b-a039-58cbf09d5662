using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.VoucherDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.ProcessFlow.Interface;

public interface IAffiliateFlow
{
    /// <summary>
    /// X<PERSON> lý thanh toán hoa hồng cho tất cả shop đang hoạt động
    /// </summary>
    /// <returns></returns>
    Task ProcessCommissionPayments();

    Task DeactivateExpiredAffiliationUsers();
}
