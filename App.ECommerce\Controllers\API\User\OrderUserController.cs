using App.Base.Middleware;
using App.Base.Repository.Interface;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Implement;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Implement;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.Transport;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using App.ECommerce.Units.Enums.Order;
using AutoMapper;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Web;
using static App.ECommerce.Controllers.API.OrderPartnerController;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_USER)]
[ApiExplorerSettings(GroupName = "user-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.User, Rules = "")]
public class OrderUserController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(OrderUserController));
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private readonly IUserRepository _userRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShippingAddressRepository _addressRepository;
    private readonly ITagRepository _tagRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IBranchRepository _branchRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IVoucherRepository _voucherRepository;
    private readonly ICartRepository _cartRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ISettingRepository _settingRepository;
    private readonly IItemOptionRepository _itemOptionRepository;
    private readonly IItemOptionGroupRepository _itemOptionGroupRepository;
    private readonly IMapper _mapper;
    private readonly ITransportService _transportService;
    private readonly IRequestLogRepository _requestLogRepository;
    private readonly IOrderToolsV2 _orderToolsV2;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly IPaymentRepository _paymentRepository;
    private readonly ITriggerEventFlow _triggerEventFlow;
    private readonly ICommissionOrderFlow _commissionOrderFlow;
    private readonly ITransportMethodRepository _transportMethodRepository;
    private readonly IInvoiceFlow _invoiceFlow;
    private readonly IOrderFlow _orderFlow;

    public OrderUserController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IUserRepository userRepository,
        IPartnerRepository partnerRepository,
        ITagRepository tagRepository,
        IShippingAddressRepository addressRepository,
        IShopRepository shopRepository,
        IBranchRepository branchRepository,
        ICategoryRepository categoryRepository,
        IItemsRepository itemsRepository,
        IVoucherRepository voucherRepository,
        ICartRepository cartRepository,
        IOrderRepository orderRepository,
        ISettingRepository settingRepository,
        IItemOptionRepository itemOptionRepository,
        IItemOptionGroupRepository itemOptionGroupRepository,
        IMapper mapper, ITransportService transportService,
        IRequestLogRepository logDataRepository,
        IOrderToolsV2 orderToolsV2,
        IPaymentRepository paymentRepository,
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IMembershipLevelRepository membershipLevelRepository,
        ITriggerEventFlow triggerEventFlow,
        ICommissionOrderFlow commissionOrderFlow,
        ITransportMethodRepository transportMethodRepository,
        IInvoiceFlow invoiceFlow,
        IOrderFlow orderFlow
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _userRepository = userRepository;
        _partnerRepository = partnerRepository;
        _tagRepository = tagRepository;
        _addressRepository = addressRepository;
        _shopRepository = shopRepository;
        _branchRepository = branchRepository;
        _categoryRepository = categoryRepository;
        _itemsRepository = itemsRepository;
        _voucherRepository = voucherRepository;
        _cartRepository = cartRepository;
        _orderRepository = orderRepository;
        _settingRepository = settingRepository;
        _itemOptionRepository = itemOptionRepository;
        _itemOptionGroupRepository = itemOptionGroupRepository;
        _mapper = mapper;
        _transportService = transportService;
        _requestLogRepository = logDataRepository;
        _orderToolsV2 = orderToolsV2;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _membershipLevelRepository = membershipLevelRepository;
        _paymentRepository = paymentRepository;
        _triggerEventFlow = triggerEventFlow;
        _commissionOrderFlow = commissionOrderFlow;
        _transportMethodRepository = transportMethodRepository;
        _invoiceFlow = invoiceFlow;
        _orderFlow = orderFlow;
    }

    /// <summary>
    /// Create order (Tạo mới đơn hàng, tạo mới transactionId is empty)
    /// </summary>
    /// <returns>The result CreateOrder for shop</returns>
    // Post: api/user/OrderUser/CreateOrder
    [HttpPost("CreateOrder")]
    public async Task<IActionResult> CreateOrder()
    {
        string requestId = Guid.NewGuid().ToString();

        try
        {
            string userId = GetUserIdAuth();

            var cart = await _cartRepository.GetCurrentCartOfUser(userId);
            if (cart == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("CART_NOT_FOUND"), this.ControllerContext));

            if (cart.StatusDelivery == TypeDelivery.ExpressDelivery && cart.TransportService == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("ORDER_TRANSPORT_SERVICE_INVALID"), this.ControllerContext));

            if (cart.StatusDelivery == TypeDelivery.InShop && string.IsNullOrEmpty(cart.BranchId))
                return ResponseBadRequest(new CustomBadRequest(localizer("BRANCH_NOT_FOUND"), this.ControllerContext));

            if (cart.StatusDelivery == TypeDelivery.ExpressDelivery && cart.AddressId == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("CART_ADDRESS_NOT_FOUND"), this.ControllerContext));

            var objOrder = new OrderFlowModel()
            {
                RequestId = requestId,
                PartnerId = null,
                CartDto = _mapper.Map<CartDto>(cart)
            };

            var result = await _orderFlow.CreateOrder(objOrder);
            if (!result.IsSuccess)
            {
                if (string.IsNullOrEmpty(result.Message))
                    return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
                else
                    return ResponseBadRequest(new CustomBadRequest($"{string.Join(", ", result.Errors.Select(e => localizer(e)))} {result.Message}", this.ControllerContext));
            }

            LogEvent(new EventLogDto
            {
                RefId = userId,
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CreateOrder",
                Message = "User CreateOrder",
                Exception = null,
                DataObject = null
            });

            return ResponseData(result.Data);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CreateOrder",
                Message = "Error User CreateOrder",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderUser/CreateOrder", ex, null);
        }
    }

    /// <summary>
    /// User Get Order Detail (Người dùng lấy thông tin chi tiết đơn hàng)
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns>The result OrderDetail</returns>
    // GET: api/partner/OrderUser/OrderDetail
    [HttpGet("OrderDetail")]
    public IActionResult OrderDetail(string orderId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            var userId = GetUserIdAuth();

            var user = _userRepository.FindByUserId(userId);

            var shop = _shopRepository.FindByShopId(user?.ShopId ?? "");
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            // if (shop.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var order = _orderRepository.FindByOrderId(orderId);
            if (order == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("ORDER_NOTFOUND"), this.ControllerContext));
            if (order.StatusDelivery == TypeDelivery.ExpressDelivery)
            {
                if (order.UserShippingAddress.UserId != userId)
                    return ResponseUnauthorized(new CustomBadRequest(localizer("ORDER_INVALID"), this.ControllerContext));
            }

            var orderDto = _mapper.Map<OrderUserDto>(order);

            // Add the tags to the order object (assuming User class has a Tags property)

            return ResponseData(orderDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/OrderDetail",
                Message = $"Error User get OrderDetail",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderDetail", ex);
        }
    }

    public class ListOrderUserDto
    {
        [DefaultValue("")]
        public string? Search { get; set; }

        [DefaultValue(TypeItems.Product)]
        public TypeItems? ItemsType { get; set; }

        [DefaultValue(ListOrderStatusEnum.All)]
        public ListOrderStatusEnum StatusOrder { get; set; } = ListOrderStatusEnum.All;
    }

    /// <summary>
    /// Get list order for User (Lấy danh sách đơn hàng của user)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result list order for User</returns>
    // GET: api/User/OrderUser/ListOrder
    [HttpGet("ListOrder")]
    public IActionResult ListOrder([FromQuery] ListOrderUserDto model, [FromQuery] int skip = 0,
        [FromQuery] int limit = 99)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            var userId = GetUserIdAuth();
            var user = _userRepository.FindByUserId(userId ?? "");
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));
            // if (model.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var shop = _shopRepository.FindByShopId(user.ShopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            // if (shop.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var paging = new Paging
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };

            var listOrder = _orderRepository.ListOrderUser(paging, userId: userId,
                statusOrder: model.StatusOrder, typeItems: model.ItemsType);
            var listOrderDto = _mapper.Map<List<OrderUserDto>>(listOrder.Result);

            return ResponseData(new { data = listOrderDto, skip, limit, total = listOrder.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/ListOrder",
                Message = $"Error Get ListOrder",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderUser/ListOrder", ex);
        }
    }

    public enum UserUpdateOrderType
    {
        CancelOrder
    }

    public class UserUpdateOrderInfoDto
    {
        [Required]
        [DefaultValue("")]
        public string OrderId { get; set; }

        public UserUpdateOrderType UpdateAction { get; set; } = UserUpdateOrderType.CancelOrder;
    }

    /// <summary>
    /// User update order info (User cập nhật trạng thái đơn hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result Update</returns>
    // PUT: api/partner/OrderUser/UpdateOrder
    [HttpPut("UpdateOrder")]
    public async Task<IActionResult> UpdateOrder([FromBody] UserUpdateOrderInfoDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string userId = GetUserIdAuth();
            var user = _userRepository.FindByUserId(userId);
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var shop = _shopRepository.FindByShopId(user?.ShopId ?? "");
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            var order = _orderRepository.FindByOrderId(model.OrderId);
            if (order == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("ORDER_NOTFOUND"), this.ControllerContext));

            // Validate update action
            var (isValid, validationMessage) = _orderFlow.ValidateOrderUpdate(order, UpdateOrderInfoActionEnum.CancelOrder);
            if (!isValid)
                return BadRequest(new CustomBadRequest(localizer(validationMessage), this.ControllerContext));

            // Xử lý cập nhật theo từng loại action
            var (updateSuccess, updateMessage) = model.UpdateAction switch
            {
                UserUpdateOrderType.CancelOrder => await _orderFlow.CancelOrder(order),
                _ => (false, localizer("UPDATE_ACTION_NOT_SUPPORTED"))
            };

            if (!updateSuccess)
                return BadRequest(new CustomBadRequest(updateMessage, this.ControllerContext));

            var orderDto = _mapper.Map<OrderUserDto>(order);

            LogEvent(new EventLogDto
            {
                RefId = userId,
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/UpdateOrder",
                Message = $"UpdateOrder Success",
                Exception = null,
                DataObject = null
            });

            return ResponseData(orderDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/UpdateOrder",
                Message = $"Error update Order",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderUser/UpdateOrder", ex);
        }
    }

    public class CreateMacRequestDto
    {
        public string Desc { get; set; }
        public List<CreateMacRequesItem> Item { get; set; }
        public string Amount { get; set; }
        public string Extradata { get; set; }
        public string Method { get; set; }
    }

    public class CreateMacRequesItem
    {
        public string Id { get; set; }
        public int Amount { get; set; }
    }

    /// <summary>
    /// Create Mac (Tạo mã bảo mật cho thanh toán)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateMac</returns>
    // POST: api/user/OrderUser/CreateMac
    [HttpPost("CreateMac")]
    public IActionResult CreateMac([FromBody] CreateMacRequestDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string userId = GetUserIdAuth();
            User? user = _userRepository.FindByUserId(userId ?? "");
            if (user == null) return ResponseUnauthorized(new CustomBadRequest("BASE_USER_AUTH_NOT_FOUND", this.ControllerContext));

            ShopSetting? setting = _settingRepository.FindByShopId(user.ShopId);
            if (setting == null) return ResponseBadRequest(new CustomBadRequest("SHOP_SETTING_NOT_FOUND", this.ControllerContext));

            // Sắp xếp key theo thứ tự từ điển
            var sortedKeys = model.GetType().GetProperties()
                .OrderBy(p => p.Name)
                .Select(p => new { Key = p.Name.ToLower(), Value = p.GetValue(model) })
                .ToList();

            // Tạo chuỗi `dataMac`
            var dataMac = string.Join("&", sortedKeys.Select(k =>
            {
                if (k.Value == null)
                    return $"{k.Key}=";

                return $"{k.Key}=" + (k.Value is string
                    ? k.Value
                    : System.Text.Json.JsonSerializer.Serialize(k.Value, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }));
            }));

            Console.WriteLine("dataMac: " + dataMac);
            Console.WriteLine("Secret Key: " + setting.CheckoutSecretKey);

            // Tạo MAC bằng HMACSHA256
            string mac = ComputeHmacSHA256(dataMac, setting.CheckoutSecretKey);

            LogEvent(new EventLogDto
            {
                RefId = user.UserId,
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CreateMac",
                Message = $"User CreateMac",
                Exception = null,
                DataObject = null
            });

            return ResponseData(mac);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CreateMac",
                Message = $"Error User CreateMac",
                Exception = null,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderUser/CreateMac", ex);
        }
    }

    public class ZaloNotifyRequestDto
    {
        public ZaloNotifyData? Data { get; set; }
        public string? Mac { get; set; }
    }

    public class ZaloNotifyData
    {
        public string? Method { get; set; }
        public string? OrderId { get; set; }
        public string? AppId { get; set; }
    }

    /// <summary>
    /// Tạo API thanh toán zalo
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result ZaloNotify</returns>
    // POST: api/user/OrderUser/ZaloNotify
    [AllowAnonymous]
    [HttpPost("ZaloNotify")]
    // [AllowAnonymous]
    public IActionResult ZaloNotify(ZaloNotifyRequestDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            // string json = System.Text.Json.JsonSerializer.Serialize(model, new JsonSerializerOptions { WriteIndented = true });
            // Console.WriteLine("ZaloNotifyRequestDto model: " + json);
            //Body Mock
            // {
            // "Data": {
            //     "Method": "COD_SANDBOX",
            //     "OrderId": "86148057980771002673060664_1739863991376",
            //     "AppId": "1238218090947479980"
            // },
            // "Mac": "67935ee56834a465b8489bfd0c21d14d61ab17410dd0615753322f443223851e"
            // }

            ShopSetting? setting = _settingRepository.FindByMiniAppId(model.Data.AppId);
            if (setting == null) return BadRequest(new { returnCode = 0, returnMessage = localizer("SHOP_SETTING_NOT_FOUND") });
            var data = model.Data;
            string str = $"appId={data.AppId}&orderId={data.OrderId}&method={data.Method}";
            string reqMac = ComputeHmacSHA256(str, setting.CheckoutSecretKey);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/ZaloNotify",
                Message = $"User call ZaloNotify",
                Exception = null,
                DataObject = null
            });

            if (reqMac == model.Mac)
            {
                return ResponseData(new { returnCode = 1, returnMessage = "Success" });
            }
            else
            {
                return BadRequest(new { returnCode = 0, returnMessage = "Fail" });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/ZaloNotify",
                Message = $"Error User call ZaloNotify",
                Exception = null,
                DataObject = null
            });

            return BadRequest(new { returnCode = 0, returnMessage = ex });
        }
    }

    private static string ComputeHmacSHA256(string data, string key)
    {
        byte[] keyBytes = Encoding.UTF8.GetBytes(key);
        byte[] dataBytes = Encoding.UTF8.GetBytes(data);

        using (var hmac = new HMACSHA256(keyBytes))
        {
            byte[] hashBytes = hmac.ComputeHash(dataBytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLower(); // Chuyển thành chuỗi hex
        }
    }

    // Model đại diện cho request từ Zalo
    public class ZaloCheckoutCallBackRequestDto
    {
        public ZaloCheckoutCallBackData? Data { get; set; }
        public string? OverallMac { get; set; }
        public string? Mac { get; set; }
    }

    // Model đại diện cho data trong request
    public class ZaloCheckoutCallBackData
    {
        public int Amount { get; set; }
        public string? Method { get; set; }
        public string? OrderId { get; set; }
        public string? AppId { get; set; }
        public string? Extradata { get; set; }
        public int ResultCode { get; set; }
        public string? Description { get; set; }
        public string? Message { get; set; }
        public string? TransId { get; set; }
        public string? PaymentChannel { get; set; }
    }

    // Model cho extraData
    public class ZaloCheckoutCallbackExtraData
    {
        public string? StoreName { get; set; }
        public string OrderId { get; set; }
        public string? Notes { get; set; }
        public string? StoreId { get; set; }
    }


    /// <summary>
    /// Checkout callback
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CheckoutCallback</returns>
    // POST: api/user/OrderUser/CheckoutCallback
    [AllowAnonymous]
    [HttpPost("CheckoutCallback")]
    // [AllowAnonymous]
    public async Task<IActionResult> CheckoutCallback(ZaloCheckoutCallBackRequestDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            // string json = System.Text.Json.JsonSerializer.Serialize(model, new JsonSerializerOptions { WriteIndented = true });
            // Console.WriteLine("ZaloCheckoutCallBackRequestDto model: " + json);
            //data mẫu model
            // var testBody =  {
            //   "Data": {
            //     "Amount": 55000,
            //     "Method": "VNPAY_SANDBOX",
            //     "OrderId": "86148057980771002673060664_1739779908210",
            //     "AppId": "1238218090947479980",
            //     "Extradata": "%7B%22storeName%22%3A%22Kho%20t%E1%BB%95ng%22%2C%22storeId%22%3A%221%22%2C%22orderId%22%3A%22cc2233b1-8a3d-482d-826a-c9f1d479f365%22%2C%22notes%22%3Anull%7D",
            //     "ResultCode": 1,
            //     "Description": "Thanh%20to%C3%A1n%200HNAF5ASIJTRM",
            //     "Message": "Giao d\u1ECBch th\u00E0nh c\u00F4ng",
            //     "TransId": "250217_1511482103695676024177431128031",
            //     "PaymentChannel": "VNPAY_SANDBOX"
            //   },
            //   "OverallMac": "9b3cbc5db97ff7023705ede1544ac348db4054ead9b56fff6637651ff41164b8",
            //   "Mac": "d68ff7dcc2433e666115c6ae22d46ba8705a5948065166adfc0beb15c8e418b3"
            // }

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CheckoutCallback",
                Message = $"IPN received CheckoutCallback",
                Exception = null,
                DataObject = null
            });

            var data = model.Data;
            string encodedExtraData = model.Data.Extradata;
            string extraDataDecoded = HttpUtility.UrlDecode(data.Extradata);
            var extraDataObj = JsonConvert.DeserializeObject<ZaloCheckoutCallbackExtraData>(extraDataDecoded);
            //Update trạng thái đơn hàng thành công dựa theo OrderId
            var order = _orderRepository.FindByOrderId(extraDataObj?.OrderId);
            if (order == null) return BadRequest(new { returnCode = 0, returnMessage = "Fail" });

            ShopSetting? setting = _settingRepository.FindByShopId(order.ShopId);
            if (setting == null) return BadRequest(new { returnCode = 0, returnMessage = localizer("SHOP_SETTING_NOT_FOUND") });
            // Tạo MAC
            string str = $"appId={data.AppId}&amount={data.Amount}&description={data.Description}&orderId={data.OrderId}&message={data.Message}&resultCode={data.ResultCode}&transId={data.TransId}";
            string reqMac = ComputeHmacSHA256(str, setting.CheckoutSecretKey);
            bool validMac = reqMac == model.Mac;
            var resultCode = model.Data.ResultCode;

            // Tạo Overall MAC
            // Chuyển đổi key thành camelCase
            string dataOverallMac = string.Join("&", data.GetType()
                .GetProperties()
                .OrderBy(p => p.Name)
                .Select(p => $"{Char.ToLowerInvariant(p.Name[0])}{p.Name.Substring(1)}={p.GetValue(data)}"));

            string reqOverallMac = ComputeHmacSHA256(dataOverallMac, setting.CheckoutSecretKey);
            bool validOverallMac = reqOverallMac == model.OverallMac;

            // Kiểm tra MAC hợp lệ
            if (validOverallMac && validMac)
            {
                if (resultCode == 1)
                {
                    order.StatusPay = TypePayStatus.Paid;
                    _orderRepository.UpdateOrder(order);

                    await _calcMemberLevelFlow.CalcUserPointByOrder(order.OrderId);

                    // Tạo đơn vận chuyển
                    if (order.TransportService == TypeTransportService.AHAMOVE || order.TransportService == TypeTransportService.JTEXPRESS)
                    {
                        var transportOrderResponse = await _transportService.CreateOrder(order);
                        if (transportOrderResponse.Success)
                        {
                            var transportOrderData = transportOrderResponse.Data;
                            order.TransportOrderId = transportOrderData.order_id;
                            _orderRepository.UpdateOrder(order);
                        }
                    }

                    await _triggerEventFlow.TriggerEventSendMessage(TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_SUCCESS, order.ShopId, order.OrderId);

                    await _invoiceFlow.IssueInvoiceAsync($"OrderUser_{requestId}", order.OrderId);

                    return ResponseData(new { returnCode = 1, returnMessage = "Success" });
                }

            }
            return BadRequest(new { returnCode = 0, returnMessage = "Fail" });
        }
        catch (Exception ex)
        {

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/CheckoutCallback",
                Message = $"Error User call ZaloNotify",
                Exception = ex,
                DataObject = null
            });

            return BadRequest(new { returnCode = 0, returnMessage = "Fail" });
        }

    }

    /// <summary>
    /// Ahamove callback
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CheckoutCallback</returns>
    // POST: api/user/OrderUser/CheckoutCallback
    [AllowAnonymous]
    [HttpPost("AhamoveCallBack")]
    public async Task<IActionResult> AhamoveCallBack()
    {
        string requestId = Guid.NewGuid().ToString();
        //body mẫu
        // {"_id":"25ASJAYV","accept_time":1741686641.9705875,"board_time":1741686747.2671068,"cancel_by_user":false,"city_id":"HAN","complete_time":1741686810.0699975,"create_time":1741685952,"currency":"VND","order_time":1741685952,"partner":"evotech","path":[{"address":"75 giải phóng, Quận Hai Bà Trưng, phường Đồng Tâm, Hà Nội","tracking_number":"16bbad45-b5a4-401c-b5fe-722e37f8e9cf","short_address":"Quận Hai Bà Trưng, phường Đồng Tâm, Hà Nội","mobile":"0388312233","status":"PICKED UP","name":"branch 1","remarks":"Đến nơi lấy hàng đọc mã đơn để nhận hàng"},{"address":"235r3 345y, Phường Tây Mỗ, Quận Nam Từ Liêm, Hà Nội","tracking_number":"16bbad45-b5a4-401c-b5fe-722e37f8e9cf","short_address":"Phường Tây Mỗ, Quận Nam Từ Liêm, Hà Nội","mobile":"+84366543346","status":"COMPLETED","complete_lat":21.003164,"complete_lng":105.8414,"complete_time":1741686809.8631728,"name":"khách hàng số bẩy"}],"payment_method":"CASH","pickup_time":1741686747.2671068,"service_id":"HAN-BIKE","status":"COMPLETED","supplier_id":"84399240358","supplier_name":"Driver","surcharge":1,"user_id":"84388316690","user_name":"Nguyễn Văn A","total_pay":71000,"distance":12.35,"duration":2927,"pickup_lat":21.003164,"pickup_lng":105.8414,"board_lat":21.003164,"board_lng":105.8414,"stoppoint_price":0,"special_request_price":0,"vat":0,"distance_price":71000,"voucher_discount":0,"subtotal_price":71000,"total_price":71000,"surge_rate":1,"cod_commission_price":0,"weight_price":0,"api_key":"64fd35f12f6b6c29dfc1d37071265b7d3b4075b9","shared_link":"https://expressstg.ahamove.com/s/250311693ITF","app":"AhaMove","store_id":0,"wh_info":{}}
        var logData = new RequestLog
        {
            RequestLogName = "Ahamove Webhook",
            Method = "POST",
            Endpoint = "/api/user/orderuser/ahamovecallback",
            RequestIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown"
        };
        try
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            logData.RequestBody = body;
            Console.WriteLine($"[AhamoveCallBack] Received body: {body}");

            var ahamoveCallback = JsonConvert.DeserializeObject<dynamic>(body);
            var transportOrderId = ahamoveCallback._id.ToString();
            var status = ahamoveCallback.status.ToString();
            var sub_status = ahamoveCallback.sub_status?.ToString() ?? "";
            var order = _orderRepository.FindByTransportOrderId(transportOrderId, TypeTransportService.AHAMOVE);

            if (order == null)
            {
                Logs.debug($"[AhamoveCallBack] Không tìm thấy đơn hàng transportOrderid: {transportOrderId}");

                logData.ResponseBody = JsonConvert.SerializeObject(new { returnCode = 0, returnMessage = "Đơn hàng không tồn tại" });
                return BadRequest(new { returnCode = 0, returnMessage = "Đơn hàng không tồn tại" });
            }
            (bool Success, string Message) result = (false, "Không xác định trạng thái");


            switch (status)
            {
                case "ASSIGNING":
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.WaitingForDelivery);
                    break;
                case "IN PROCESS":
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Delivering);
                    break;
                case "COMPLETED":
                    if (sub_status == "RETURNED")
                    {
                        result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Refunded, TypeOrderStatus.Refund);
                    }
                    else if (string.IsNullOrEmpty(sub_status)) // Chỉ khi sub_status rỗng hoặc null
                    {
                        result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Success, TypeOrderStatus.Success);

                        await _triggerEventFlow.TriggerEventSendMessage(
                            TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY_SUCCESS,
                            order.ShopId,
                            order.OrderId
                        );
                    }
                    break;
                case "CANCELLED":
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Cancel, TypeOrderStatus.Failed);
                    break;
            }

            Logs.debug($"[AhamoveCallBack] Cập nhật trạng thái đơn hàng orderNo {order.OrderNo} thành công: {status}");

            LogEvent(new EventLogDto
            {
                RefId = order.Creator?.UserId,
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/AhamoveCallBack",
                Message = $"IPN received AhamoveCallBack",
                Exception = null,
                DataObject = null
            });

            logData.ResponseStatusCode = 200;
            logData.ResponseBody = JsonConvert.SerializeObject(new { returnCode = 1, returnMessage = result.Message });
            return Ok(new { returnCode = 1, returnMessage = result.Message });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/AhamoveCallBack",
                Message = $"Error IPN received AhamoveCallBack",
                Exception = null,
                DataObject = null
            });

            logData.ResponseStatusCode = 500;
            logData.ErrorMessage = ex.Message;
            logData.ResponseBody = JsonConvert.SerializeObject(new { returnCode = 0, returnMessage = "Fail" });
            return BadRequest(new { returnCode = 0, returnMessage = "Fail" });
        }
        finally
        {
            _requestLogRepository.CreateRequestLog(logData);
        }
    }

    /// <summary>
    /// Mục đích của webhook này là cung cấp cho đối tác đầy đủ thông tin cần thiết để hoàn tất hồ sơ kiểm thử trong môi trường Sandbox (SIT Test) theo yêu cầu của VNPay.
    /// </summary>
    /// <returns>The result ZaloVnpayIPN</returns>
    // POST: api/user/OrderUser/ZaloVnpayIPN
    [AllowAnonymous]
    [HttpPost("ZaloVnpayIPN")]
    public async Task<IActionResult> ZaloVnpayIPN()
    {
        string requestId = Guid.NewGuid().ToString();
        var logData = new RequestLog
        {
            RequestLogName = "Zalo Vnpay IPN",
            Method = "POST",
            Endpoint = "/api/user/OrderUser/zalovnpayipn",
            RequestIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown"
        };
        try
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            logData.RequestBody = body;
            Console.WriteLine($"[ZaloVnpayIPN] Received body: {body}");
            logData.ResponseStatusCode = 200;
            logData.ResponseBody = JsonConvert.SerializeObject(new { returnCode = 1, returnMessage = "Success" });
            return Ok(new { returnCode = 1, returnMessage = "Success" });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/ZaloVnpayIPN",
                Message = $"Error IPN received ZaloVnpayIPN",
                Exception = ex,
                DataObject = null
            });

            logData.ResponseStatusCode = 500;
            logData.ErrorMessage = ex.Message;
            logData.ResponseBody = JsonConvert.SerializeObject(new { returnCode = 0, returnMessage = "Fail" });
            return BadRequest(new { returnCode = 0, returnMessage = "Fail" });
        }
        finally
        {
            _requestLogRepository.CreateRequestLog(logData);
        }
    }

    /// <summary>
    /// JTExpress CallBack
    /// </summary>
    /// <returns>The result JTExpressCallBack</returns>
    [AllowAnonymous]
    [HttpPost("JTExpressCallBack")]
    public async Task<IActionResult> JTExpressCallBack()
    {
        string requestId = Guid.NewGuid().ToString();

        var logData = new RequestLog
        {
            RequestLogName = "JTExpress Webhook",
            Method = "POST",
            Endpoint = "/api/user/orderuser/JTExpressCallBack",
            RequestIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown",
            RequestHeaders = JsonConvert.SerializeObject(Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()))
        };
        try
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine($"[JTExpressCallBack] Received body: {body}");
            logData.RequestBody = body;

            // Parse form-urlencoded string
            var parsed = HttpUtility.ParseQueryString(body);
            var bizContentEncoded = parsed["bizContent"];

            if (string.IsNullOrEmpty(bizContentEncoded))
            {
                logData.ErrorMessage = "Missing bizContent";
                return Ok(new { code = 0, msg = "Missing bizContent", data = "" });
            }

            var json = Uri.UnescapeDataString(bizContentEncoded);

            Console.WriteLine($"[JTExpressCallBack] Parsed JSON: {json}");

            var jtExpressBody = JsonConvert.DeserializeObject<JTExpressWebhookRequestDto>(json);
            var transportOrderId = jtExpressBody.billCode;
            var scanTypeCode = jtExpressBody.details[0].scanTypeCode;
            var order = _orderRepository.FindByTransportOrderId(transportOrderId, TypeTransportService.JTEXPRESS);

            if (order == null)
            {
                logData.ResponseBody = JsonConvert.SerializeObject(new { code = 0, msg = "Không thấy đơn hàng", data = "" });
                return Ok(new { code = 0, msg = "Không thấy đơn hàng", data = "" });
            }

            var transportMethod = _transportMethodRepository.FindByTransportCode(TransportCodeType.JTExpress, order.ShopId);

            var genDigest = _transportService.GenerateDigest(json, _transportService.GetJTExpressPrivateKey());
            var headerDigest = Request.Headers["digest"].ToString();
            if (genDigest != headerDigest)
            {
                logData.ErrorMessage = "Digest không hợp lệ";
                logData.ResponseBody = JsonConvert.SerializeObject(new { code = 0, msg = "Digest không hợp lệ" });
                return Ok(new { code = 0, msg = "Digest không hợp lệ", data = "" });
            }

            (bool Success, string Message) result = (false, "Không xác định trạng thái");


            // Cập nhật trạng thái đơn hàng theo status của JT
            switch (scanTypeCode)
            {
                case 106: // Đang tìm tài xế
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.WaitingForDelivery);
                    break;
                case 112: // Tài xế đã nhận đơn
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Delivering);
                    break;
                case 113: // Đơn hàng đã giao thành công
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Success, TypeOrderStatus.Success);
                    break;
                case 117: //Đơn hàng đã hoàn trả
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Refunded, TypeOrderStatus.Refund);
                    break;
                case 105: // Đơn hàng đã hủy
                    result = await _orderFlow.UpdateOrderStatus(order, TypeTransportStatus.Cancel, TypeOrderStatus.Failed);
                    break;
            }

            Logs.debug($"[JTExpressCallBack] Cập nhật trạng thái đơn hàng orderNo {order.OrderNo} thành công: {scanTypeCode}");

            LogEvent(new EventLogDto
            {
                RefId = order.Creator?.UserId,
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/JTExpressCallBack",
                Message = $"IPN received JTExpressCallBack",
                Exception = null,
                DataObject = null
            });

            logData.ResponseStatusCode = 200;
            logData.ResponseBody = JsonConvert.SerializeObject(new { code = 1, msg = result.Message, data = "" });
            return Ok(new { code = 1, msg = result.Message, data = "" });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/JTExpressCallBack",
                Message = $"Error IPN received JTExpressCallBack",
                Exception = null,
                DataObject = null
            });

            logData.ResponseStatusCode = 500;
            logData.ErrorMessage = ex.Message;
            logData.ResponseBody = JsonConvert.SerializeObject(new { code = 0, msg = "Fail", data = "" });
            return Ok(new { code = 0, msg = "Fail", data = "" });
        }
        finally
        {
            _requestLogRepository.CreateRequestLog(logData);
        }
    }

    /// <summary>
    /// Toltal Order Status (Tổng số lượng đơn hàng theo trạng thái)
    /// </summary>
    [HttpGet("totalstatus")]
    public async Task<IActionResult> GetTotalOrderStatus([FromQuery] string shopId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            var userId = GetUserIdAuth();
            var user = _userRepository.FindByUserId(userId ?? "");
            if (user == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"),
                    this.ControllerContext));

            var shop = _shopRepository.FindByShopId(user.ShopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            var result = await _orderRepository.GetTotalOrderStatus(user.ShopId, userId);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderUser/totalstatus",
                Message = $"Error GetTotalOrderStatus",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderUser/totalstatus", ex);
        }
    }
}
