using Newtonsoft.Json;

namespace App.ECommerce.Resource.Dtos.NhanhDtos;

/// <summary>
/// Response data cho API product search từ Nhanh.vn
/// </summary>
public class NhanhProductSearchResponse
{
    /// <summary>
    /// Trang hiện tại
    /// </summary>
    [JsonProperty("currentPage")]
    public int CurrentPage { get; set; }

    /// <summary>
    /// Tổng số trang
    /// </summary>
    [JsonProperty("totalPages")]
    public int TotalPages { get; set; }

    /// <summary>
    /// Dictionary chứa danh sách sản phẩm với key là ID sản phẩm
    /// </summary>
    [JsonProperty("products")]
    public Dictionary<string, NhanhProductDetailDto> Products { get; set; }
}

/// <summary>
/// Chi tiết sản phẩm từ API search của Nhanh.vn
/// </summary>
public class NhanhProductDetailDto
{
    [JsonProperty("idNhanh")]
    public int IdNhanh { get; set; }

    [JsonProperty("parentId")]
    public int? ParentId { get; set; }

    [JsonProperty("brandId")]
    public int? BrandId { get; set; }

    [JsonProperty("brandName")]
    public string? BrandName { get; set; }

    [JsonProperty("typeId")]
    public string? TypeId { get; set; }

    [JsonProperty("typeName")]
    public string? TypeName { get; set; }

    [JsonProperty("avgCost")]
    public string? AvgCost { get; set; }

    [JsonProperty("merchantCategoryId")]
    public string? MerchantCategoryId { get; set; }

    [JsonProperty("merchantProductId")]
    public string? MerchantProductId { get; set; }

    [JsonProperty("categoryId")]
    public string? CategoryId { get; set; }

    [JsonProperty("internalCategoryId")]
    public string? InternalCategoryId { get; set; }

    [JsonProperty("code")]
    public string? Code { get; set; }

    [JsonProperty("barcode")]
    public string? Barcode { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("otherName")]
    public string? OtherName { get; set; }

    [JsonProperty("importPrice")]
    public string? ImportPrice { get; set; }

    [JsonProperty("oldPrice")]
    public string? OldPrice { get; set; }

    [JsonProperty("price")]
    public string? Price { get; set; }

    [JsonProperty("wholesalePrice")]
    public string? WholesalePrice { get; set; }

    [JsonProperty("image")]
    public string? Image { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("showHot")]
    public int? ShowHot { get; set; }

    [JsonProperty("showNew")]
    public int? ShowNew { get; set; }

    [JsonProperty("showHome")]
    public int? ShowHome { get; set; }

    [JsonProperty("order")]
    public string? Order { get; set; }

    [JsonProperty("previewLink")]
    public string? PreviewLink { get; set; }

    [JsonProperty("shippingWeight")]
    public string? ShippingWeight { get; set; }

    [JsonProperty("width")]
    public string? Width { get; set; }

    [JsonProperty("length")]
    public string? Length { get; set; }

    [JsonProperty("height")]
    public string? Height { get; set; }

    [JsonProperty("vat")]
    public string? Vat { get; set; }

    [JsonProperty("createdDateTime")]
    public string? CreatedDateTime { get; set; }

    [JsonProperty("inventory")]
    public NhanhProductInventoryDetailDto? Inventory { get; set; }

    [JsonProperty("warrantyAddress")]
    public string? WarrantyAddress { get; set; }

    [JsonProperty("warrantyPhone")]
    public string? WarrantyPhone { get; set; }

    [JsonProperty("warranty")]
    public string? Warranty { get; set; }

    [JsonProperty("countryName")]
    public string? CountryName { get; set; }

    [JsonProperty("unit")]
    public string? Unit { get; set; }

    [JsonProperty("updatedAt")]
    public long? UpdatedAt { get; set; }

    [JsonProperty("attributes")]
    public List<Dictionary<string, NhanhProductAttributeDetailDto>>? Attributes { get; set; }
}

/// <summary>
/// Chi tiết inventory từ API search
/// </summary>
public class NhanhProductInventoryDetailDto
{
    [JsonProperty("remain")]
    public int? Remain { get; set; }

    [JsonProperty("shipping")]
    public int? Shipping { get; set; }

    [JsonProperty("damaged")]
    public int? Damaged { get; set; }

    [JsonProperty("holding")]
    public int? Holding { get; set; }

    [JsonProperty("warranty")]
    public int? Warranty { get; set; }

    [JsonProperty("warrantyHolding")]
    public int? WarrantyHolding { get; set; }

    [JsonProperty("transfering")]
    public int? Transfering { get; set; }

    [JsonProperty("available")]
    public int? Available { get; set; }

    [JsonProperty("depots")]
    public List<object>? Depots { get; set; }
}

/// <summary>
/// Chi tiết attribute từ API search
/// </summary>
public class NhanhProductAttributeDetailDto
{
    [JsonProperty("attributeName")]
    public string? AttributeName { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("value")]
    public string? Value { get; set; }

    [JsonProperty("order")]
    public int? Order { get; set; }
}
