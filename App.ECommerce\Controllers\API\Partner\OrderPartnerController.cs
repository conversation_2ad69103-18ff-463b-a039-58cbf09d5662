using App.Base.Middleware;
using App.Base.Repository.Interface;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Implement;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.Transport;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using App.ECommerce.Units.Enums.Order;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net;
using System.Threading.Tasks;

using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class OrderPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(OrderPartnerController));
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private readonly IUserRepository _userRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IBranchRepository _branchRepository;
    private readonly ITransportService _transportService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOrderFlow _orderFlow;

    public OrderPartnerController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IUserRepository userRepository,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        IOrderRepository orderRepository,
        IBranchRepository branchRepository,
        ITransportService transportService,
        IHttpClientFactory httpClientFactory,
        IOrderFlow orderFlow
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _userRepository = userRepository;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _orderRepository = orderRepository;
        _branchRepository = branchRepository;
        _transportService = transportService;
        _httpClientFactory = httpClientFactory;
        _orderFlow = orderFlow;
    }

    /// <summary>
    /// Create order (Tạo mới đơn hàng, tạo mới transactionId is empty)
    /// </summary>
    /// <param name="obj"></param>
    /// <returns>The result CreateOrder for shop</returns>
    // Post: api/partner/OrderPartner/CreateOrder
    [HttpPost("CreateOrder")]
    public async Task<IActionResult> CreateOrder(CartDto obj)
    {
        var requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            partnerId = !string.IsNullOrEmpty(partner.ParentId)
                                               ? partner.ParentId
                                               : partnerId;

            var objOrder = new OrderFlowModel()
            {
                RequestId = requestId,
                PartnerId = partnerId,
                CartDto = obj
            };

            var result = await _orderFlow.CreateOrder(objOrder);

            if (!result.IsSuccess)
            {
                if (string.IsNullOrEmpty(result.Message))
                    return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
                else
                    return ResponseBadRequest(new CustomBadRequest($"{string.Join(", ", result.Errors.Select(e => localizer(e)))} {result.Message}", this.ControllerContext));
            }

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/CreateOrder",
                Message = "Partner CreateOrder",
                Exception = null,
                DataObject = null
            });

            return ResponseData(result.Data);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/CreateOrder",
                Message = $"Error Partner CreateOrder",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/OrderPartner/CreateOrder", ex, obj);
        }
    }

    /// <summary>
    /// Get list order for Partner (Lấy danh sách đơn hàng cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result list order for Partner</returns>
    // GET: api/partner/OrderPartner/ListOrder
    [HttpGet("ListOrder")]
    public async Task<IActionResult> ListOrder([FromQuery] ListOrderInputDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<Order> listOrder = await _orderRepository.ListOrderExtend(paging, model);

            List<OrderPartnerDto> listOrderDto = _mapper.Map<List<OrderPartnerDto>>(listOrder.Result);

            return ResponseData(new { data = listOrderDto, skip, limit, total = listOrder.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/ListOrder",
                Message = $"Error Partner get list Order",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/OrderPartner/ListOrder", ex);
        }
    }

    /// <summary>
    /// Get Order Detail (Lấy thông tin chi tiết đơn hàng)
    /// </summary>
    /// <param name="orderId"></param>
    /// <param name="shopId"></param>
    /// <returns>The result OrderDetail</returns>
    // GET: api/partner/OrderPartner/OrderDetail
    [HttpGet("OrderDetail")]
    public async Task<IActionResult> OrderDetail(string orderId, string shopId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var order = _orderRepository.FindByOrderId(orderId);
            if (order == null) return ResponseUnauthorized(new CustomBadRequest(localizer("ORDER_NOTFOUND"), this.ControllerContext));
            OrderPartnerDto orderDto = _mapper.Map<OrderPartnerDto>(order);

            // Add the tags to the order object (assuming User class has a Tags property)

            return ResponseData(orderDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/OrderDetail",
                Message = $"Error OrderDetail partner",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/OrderDetail", ex);
        }
    }

    /// <summary>
    /// Partner update order info (Đối tác cập nhật thông tin đơn hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result Update</returns>
    // PUT: api/partner/OrderPartner/UpdateOrderInfo
    [HttpPut("UpdateOrderInfo")]
    public async Task<IActionResult> UpdateOrderInfo([FromBody] UpdateOrderInfoDto model)
    {
        var requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var order = _orderRepository.FindByOrderId(model.OrderId);
            if (order == null) return BadRequest(new CustomBadRequest(localizer("ORDER_NOTFOUND"), this.ControllerContext));

            // Validate update action
            var (isValid, validationMessage) = _orderFlow.ValidateOrderUpdate(order, model.UpdateAction);
            if (!isValid)
                return BadRequest(new CustomBadRequest(localizer(validationMessage), this.ControllerContext));

            // Xử lý cập nhật theo từng loại action
            var (updateSuccess, updateMessage) = model.UpdateAction switch
            {
                UpdateOrderInfoActionEnum.StatusDelivery => await _orderFlow.UpdateOrderStatus(
                    order,
                    order.StatusTransport == TypeTransportStatus.Created || order.StatusTransport == TypeTransportStatus.WaitingForDelivery
                        ? TypeTransportStatus.Delivering
                        : TypeTransportStatus.Success,
                    order.StatusTransport == TypeTransportStatus.Delivering && order.StatusPay == TypePayStatus.Paid
                        ? TypeOrderStatus.Success
                        : null
                ),

                UpdateOrderInfoActionEnum.UpdateNotes => await _orderFlow.UpdateOrderNotes(order, model.Notes),

                UpdateOrderInfoActionEnum.CancelOrder => await _orderFlow.CancelOrder(order),

                UpdateOrderInfoActionEnum.StatusPay => await _orderFlow.UpdateOrderPayment(order),

                _ => (false, localizer("UPDATE_ACTION_NOT_SUPPORTED"))
            };

            if (!updateSuccess)
                return BadRequest(new CustomBadRequest(updateMessage, this.ControllerContext));

            // Cập nhật thông tin chi nhánh nếu có
            if (!string.IsNullOrEmpty(model.BranchId) && string.IsNullOrEmpty(order.BranchId))
            {
                var branchInfo = _branchRepository.FindByBranchId(model.BranchId);
                if (branchInfo != null)
                {
                    order.BranchId = model.BranchId;
                    order.BranchDetail = branchInfo;
                    _orderRepository.UpdateOrder(order);
                }
            }

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/UpdateOrderInfo",
                Message = $"Partner update Order info",
                Exception = null,
                DataObject = order
            });

            OrderPartnerDto orderDto = _mapper.Map<OrderPartnerDto>(order);
            return ResponseData(orderDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/UpdateOrderInfo",
                Message = $"Error Partner update Order info",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/OrderPartner/UpdateOrderInfo", ex);
        }
    }

    /// <summary>
    /// Get list order of an user for Partner (Lấy danh sách đơn hàng của 1 khách hàng cho đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result list order for Partner</returns>
    // GET: api/partner/OrderPartner/ListOrder
    [HttpGet("ListOrderByUserId")]
    public async Task<IActionResult> ListOrderByUserId([FromQuery] ListOrderByUserIdInputDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            // if (model.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            User? user = _userRepository.FindByUserId(model.UserId);
            if (user == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(user.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<Order> listOrder = _orderRepository.ListOrderUser(paging, model.UserId, statusOrder: ListOrderStatusEnum.All, typeItems: null);
            List<OrderPartnerDto> listOrderDto = _mapper.Map<List<OrderPartnerDto>>(listOrder.Result);
            return ResponseData(new { data = listOrderDto, skip, limit, total = listOrder.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_PARTNER}/OrderPartner/ListOrderByUserId",
                Message = $"Error Partner get list Order by userId",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/OrderPartner/ListOrderByUserId", ex);
        }
    }

    [HttpGet("ExportOrderExcel")]
    public async Task<IActionResult> ExportOrderExcel([FromQuery] ListOrderInputDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = 0,
                PageSize = int.MaxValue,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc,
            };

            PagingResult<Order> listOrder = await _orderRepository.ListOrderExtend(paging, model);
            List<OrderPartnerDto> listOrderDto = _mapper.Map<List<OrderPartnerDto>>(listOrder.Result);

            var fileBytes = await _orderRepository.ExportListOrder(listOrderDto);
            // Gợi ý đặt header tên file có ngày
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            var fileName = $"DanhSachDonHang_{timestamp}.xlsx";

            // Trả về file Excel
            return File(fileBytes,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                fileName);


        }
        catch (Exception ex)
        {
            _log4net.Error(ex);

            LogExceptionEvent(_log4net, "ExportOrder", ex, null);

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Partner PrintTransportOrder (Đối tác in hóa đơn đơn hàng)
    /// </summary>
    /// <param name="model">ID đơn hàng cần in hóa đơn</param>
    /// <returns>Result of printing (file PDF merged)</returns>
    [HttpPost("PrintTransportOrders")]
    public async Task<IActionResult> PrintTransportOrders([FromBody] PrintTransportOrdersDto model)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            var partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            var orders = _orderRepository.FindByOrderIds(model.OrderIds)
                .Where(o => !string.IsNullOrWhiteSpace(o.TransportOrderLabel))
                .ToList();

            var listOrderDto = _mapper.Map<List<OrderPartnerDto>>(orders);
            var pdfBytesList = new List<byte[]>();

            foreach (var order in listOrderDto)
            {
                var url = order.TransportOrderLabel?.Trim();
                if (string.IsNullOrWhiteSpace(url) || !Uri.IsWellFormedUriString(url, UriKind.Absolute))
                {
                    continue;
                }

                try
                {
                    var client = _httpClientFactory.CreateClient();
                    var pdfBytes = await client.GetByteArrayAsync(url);
                    pdfBytesList.Add(pdfBytes);
                }
                catch (Exception ex)
                {
                    LogEvent(new EventLogDto
                    {
                        RefId = order.OrderId,
                        RefType = TypeFor.Partner,
                        Action = LogActionEnum.Print,
                        Status = LogStatusEnum.Error,
                        ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/PrintTransportOrders",
                        Message = $"Error fetching PDF for order {order.OrderId}",
                        Exception = ex,
                        DataObject = null
                    });
                }
            }

            if (!pdfBytesList.Any())
                return BadRequest("No valid PDF files to merge.");

            using var outputStream = new MemoryStream();
            MergePdfsWithPdfSharpCore(pdfBytesList, outputStream);
            var mergedBytes = outputStream.ToArray();
            var fileName = $"PhieuVanChuyen_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.pdf";
            Response.Headers["Content-Disposition"] = $"inline; filename={fileName}";
            return File(mergedBytes, "application/pdf");
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/PrintTransportOrder",
                Message = $"Error Partner PrintTransportOrder",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/OrderPartner", ex);
        }
    }

    private void MergePdfsWithPdfSharpCore(List<byte[]> inputPdfBytes, Stream outputStream)
    {
        var outputDocument = new PdfSharpCore.Pdf.PdfDocument();

        foreach (var pdfBytes in inputPdfBytes)
        {
            using var inputStream = new MemoryStream(pdfBytes);
            var inputDocument = PdfSharpCore.Pdf.IO.PdfReader.Open(inputStream, PdfSharpCore.Pdf.IO.PdfDocumentOpenMode.Import);

            for (int i = 0; i < inputDocument.PageCount; i++)
            {
                var page = inputDocument.Pages[i];
                outputDocument.AddPage(page);
            }
        }

        outputDocument.Save(outputStream);
    }

    /// <summary>
    /// Partner TraceTransportOrder (Đối tác ta cứu đơn đơn hàng)
    /// </summary>
    /// <param name="orderId">ID đơn hàng cần tra cứu</param>
    /// <returns>Result of TraceTransportOrder</returns>
    [HttpGet("TraceTransportOrder")]
    public async Task<IActionResult> TraceTransportOrder(string orderId)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner? partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var order = _orderRepository.FindByOrderId(orderId);
            if (order == null) return BadRequest(new CustomBadRequest(localizer("ORDER_NOTFOUND"), this.ControllerContext));


            // Gọi API tra cứu đơn vận chuyển
            TransportResponse<string> traceResult;
            switch (order.TransportService)
            {
                case TypeTransportService.JTEXPRESS:
                    traceResult = await _transportService.TraceTransportOrder(order);
                    break;

                default:
                    return BadRequest(new CustomBadRequest(localizer("UNSUPPORTED_TRANSPORT_CODE"), this.ControllerContext));
            }

            // Kiểm tra kết quả từ API in hóa đơn
            if (!traceResult.Success)
            {
                return BadRequest(new CustomBadRequest(localizer("TRACE_TRANSPORT_ORDER_FAILED"), this.ControllerContext));
            }

            // Trả về kết quả (URL hoặc Base64 PDF)
            return ResponseData(new { order.TransportService, BillData = traceResult.Data });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/OrderPartner/TraceTransportOrder",
                Message = $"Error Partner TraceTransportOrder",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/OrderPartner", ex);
        }
    }


    /// <summary>
    /// Toltal Order Status (Tổng số lượng đơn hàng theo trạng thái)
    /// </summary>
    [HttpGet("totalstatus")]
    public async Task<IActionResult> GetTotalOrderStatus([FromQuery] ListOrderInputDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var result = await _orderRepository.GetTotalOrderStatusByFilter(model);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.User,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_USER}/OrderPartner/totalstatus",
                Message = $"Error GetTotalOrderStatus Partner",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_USER}/OrderPartner/totalstatus", ex);
        }
    }
}