using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Units.Enums;

using log4net;

using Quartz;

namespace App.ECommerce.Services.Jobs.Job;

/// <summary>
/// Job đồng bộ sản phẩm từ Nhanh.vn mỗi ngày
/// </summary>
public class SyncNhanhProductsJob : IJob
{
    private readonly INhanhHelper _nhanhHelper;
    private readonly ISyncServiceConfigRepository _syncConfigRepository;
    private readonly IShopRepository _shopRepository;
    private readonly ILog _log = LogManager.GetLogger(typeof(SyncNhanhProductsJob));

    // Cấu hình rate limiting
    private const int BASE_DELAY_MS = 3000; // 3 giây delay cơ bản giữa các API calls
    private const int PARENT_PRODUCTS_DELAY_MS = 2000; // 2 giây delay cho parent products
    private const int MAX_RETRY_ATTEMPTS = 3; // Số lần retry tối đa
    private const int INITIAL_RETRY_DELAY_MS = 2000; // Delay ban đầu khi retry (2 giây)

    public SyncNhanhProductsJob(
        INhanhHelper nhanhHelper,
        ISyncServiceConfigRepository syncConfigRepository,
        IShopRepository shopRepository)
    {
        _nhanhHelper = nhanhHelper;
        _syncConfigRepository = syncConfigRepository;
        _shopRepository = shopRepository;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var requestId = Guid.NewGuid().ToString();

        try
        {
            _log.Info($"{requestId} SyncNhanhProductsJob bắt đầu thực hiện");

            // Lấy tất cả các shop có cấu hình Nhanh.vn
            var nhanhConfigs = await _syncConfigRepository.GetAllConfigsByService(SyncServiceEnum.NhanhVN);

            if (nhanhConfigs == null || !nhanhConfigs.Any())
            {
                _log.Info($"{requestId} Không tìm thấy cấu hình Nhanh.vn nào");
                return;
            }

            _log.Info($"{requestId} Tìm thấy {nhanhConfigs.Count} cấu hình Nhanh.vn");

            foreach (var config in nhanhConfigs)
            {
                try
                {
                    await SyncProductsForShop(config.ShopId, requestId);
                }
                catch (Exception ex)
                {
                    _log.Error($"{requestId} Lỗi khi đồng bộ sản phẩm cho shop {config.ShopId}: {ex.Message}", ex);
                }
            }

            _log.Info($"{requestId} SyncNhanhProductsJob hoàn thành");
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} SyncNhanhProductsJob thất bại: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Đồng bộ sản phẩm cho một shop cụ thể
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="requestId">ID request để tracking log</param>
    private async Task SyncProductsForShop(string shopId, string requestId)
    {
        _log.Info($"{requestId} Bắt đầu đồng bộ sản phẩm cho shop: {shopId}");
        _log.Info($"{requestId} Cấu hình rate limiting: BASE_DELAY={BASE_DELAY_MS}ms, PARENT_DELAY={PARENT_PRODUCTS_DELAY_MS}ms, MAX_RETRY={MAX_RETRY_ATTEMPTS}");

        var shop = _shopRepository.FindByShopId(shopId);
        if (shop == null)
        {
            _log.Info($"{requestId} Không tìm thấy shop với ID: {shopId}");
            return;
        }

        // Lấy tất cả categories một lần duy nhất trước khi xử lý sản phẩm
        _log.Info($"{requestId} Đang lấy danh sách categories cho shop: {shopId}");
        var categoriesResult = await _nhanhHelper.GetNhanhCategoriesAsync(shopId);
        if (!categoriesResult.IsSuccess)
        {
            _log.Error($"{requestId} Không thể lấy categories cho shop {shopId}");
            return;
        }

        var categories = categoriesResult.Data;
        _log.Info($"{requestId} Đã lấy được {categories?.Count ?? 0} categories cho shop: {shopId}");

        // Lấy tất cả parent products trước để cache
        _log.Info($"{requestId} Đang lấy tất cả parent products cho shop: {shopId}");
        var allParentProducts = await GetAllParentProducts(shopId, requestId);
        _log.Info($"{requestId} Đã lấy được {allParentProducts.Count} parent products cho shop: {shopId}");

        int page = 1;
        int totalSynced = 0;
        int totalErrors = 0;
        int maxPages = 1000;

        while (page <= maxPages)
        {
            try
            {
                // Lấy danh sách sản phẩm từ Nhanh.vn với retry mechanism
                var productsResult = await ExecuteWithRetry(
                    () => _nhanhHelper.GetNhanhProductsAsync(shopId, page),
                    requestId,
                    $"GetNhanhProductsAsync cho shop {shopId}, trang {page}"
                );

                if (!productsResult.IsSuccess)
                {
                    totalErrors++;
                    _log.Error($"{requestId} Lỗi khi lấy sản phẩm từ Nhanh.vn cho shop {shopId}, trang {page}. Chuyển sang trang tiếp theo.");

                    // Chuyển sang trang tiếp theo khi gặp lỗi
                    page++;
                    continue;
                }

                var response = productsResult.Data;

                if (response == null || response.Products == null || !response.Products.Any())
                {
                    _log.Info($"{requestId} Không có sản phẩm nào ở trang {page} cho shop {shopId}");
                    break;
                }

                _log.Info($"{requestId} Lấy được {response.Products.Count} sản phẩm từ trang {page}/{response.TotalPages} cho shop {shopId}");
                Console.WriteLine($"DEBUG: CurrentPage={response.CurrentPage}, TotalPages={response.TotalPages}, Page={page}");

                // Đồng bộ từng sản phẩm
                foreach (var productKvp in response.Products)
                {
                    try
                    {
                        await SyncSingleProductWithParentCache(productKvp.Value, shopId, categories, allParentProducts, requestId);
                        totalSynced++;
                    }
                    catch (Exception ex)
                    {
                        _log.Error($"{requestId} Lỗi khi đồng bộ sản phẩm {productKvp.Key} cho shop {shopId}: {ex.Message}", ex);
                        // Không dừng job khi lỗi đồng bộ từng sản phẩm, tiếp tục với sản phẩm tiếp theo
                    }
                }

                // Kiểm tra xem đã đến trang cuối chưa
                Console.WriteLine($"DEBUG: Checking if page {page} >= TotalPages {response.TotalPages}");
                if (page >= response.TotalPages)
                {
                    Console.WriteLine($"DEBUG: Breaking loop - reached last page");
                    break;
                }

                page++;
                Console.WriteLine($"DEBUG: Moving to next page: {page}");

                // Thêm delay để tránh rate limiting từ NhanhVN API
                await Task.Delay(BASE_DELAY_MS);
            }
            catch (Exception ex)
            {
                _log.Error($"{requestId} Lỗi khi xử lý trang {page} cho shop {shopId}: {ex.Message}", ex);
                totalErrors++;
                break;
            }
        }

        _log.Info($"{requestId} Hoàn thành đồng bộ cho shop {shopId}. Tổng cộng đã đồng bộ: {totalSynced} sản phẩm, gặp {totalErrors} lỗi");
    }

    /// <summary>
    /// Thực hiện API call với exponential backoff retry khi gặp lỗi
    /// </summary>
    /// <typeparam name="T">Kiểu dữ liệu trả về</typeparam>
    /// <param name="apiCall">Function thực hiện API call</param>
    /// <param name="requestId">ID request để tracking log</param>
    /// <param name="operationName">Tên operation để log</param>
    /// <returns>Kết quả API call</returns>
    private async Task<T> ExecuteWithRetry<T>(Func<Task<T>> apiCall, string requestId, string operationName)
    {
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++)
        {
            try
            {
                var result = await apiCall();

                // Nếu thành công ở lần đầu, không cần delay
                if (attempt == 1)
                    return result;

                // Nếu thành công sau retry, log thông tin và trả về kết quả
                _log.Info($"{requestId} {operationName} thành công sau {attempt} lần thử");
                return result;
            }
            catch (Exception ex)
            {
                if (attempt == MAX_RETRY_ATTEMPTS)
                {
                    _log.Error($"{requestId} {operationName} thất bại sau {MAX_RETRY_ATTEMPTS} lần thử: {ex.Message}", ex);
                    throw;
                }

                // Tính toán delay với exponential backoff: 2s, 4s, 8s...
                var delayMs = INITIAL_RETRY_DELAY_MS * (int)Math.Pow(2, attempt - 1);

                // Nếu là rate limiting error, tăng delay thêm 50%
                if (IsRateLimitError(ex))
                {
                    delayMs = (int)(delayMs * 1.5);
                    _log.Warn($"{requestId} {operationName} gặp rate limiting lần {attempt}/{MAX_RETRY_ATTEMPTS}, retry sau {delayMs}ms: {ex.Message}");
                }
                else
                {
                    _log.Warn($"{requestId} {operationName} thất bại lần {attempt}/{MAX_RETRY_ATTEMPTS}, retry sau {delayMs}ms: {ex.Message}");
                }

                await Task.Delay(delayMs);
            }
        }

        // Không bao giờ đến đây, nhưng cần để compiler không báo lỗi
        throw new InvalidOperationException("Unexpected end of retry loop");
    }

    /// <summary>
    /// Kiểm tra xem lỗi có phải là rate limiting không
    /// </summary>
    /// <param name="exception">Exception cần kiểm tra</param>
    /// <returns>True nếu là rate limiting error</returns>
    private static bool IsRateLimitError(Exception exception)
    {
        var message = exception.Message?.ToLower() ?? "";
        return message.Contains("rate limit") ||
               message.Contains("too many requests") ||
               message.Contains("429") ||
               message.Contains("quota exceeded") ||
               message.Contains("api limit");
    }

    /// <summary>
    /// Đồng bộ một sản phẩm cụ thể
    /// </summary>
    /// <param name="nhanhProduct">Sản phẩm từ Nhanh.vn</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="categories">Danh sách categories đã lấy từ Nhanh.vn</param>
    /// <param name="requestId">ID request để tracking log</param>
    private async Task SyncSingleProduct(NhanhProductDetailDto nhanhProduct, string shopId, List<NhanhProductCategoryDto> categories, string requestId)
    {
        // Chuyển đổi NhanhProductDetailDto thành NhanhProductWebhookDto để sử dụng logic đồng bộ hiện có
        var webhookDto = new NhanhProductWebhookDto
        {
            ProductId = nhanhProduct.IdNhanh,
            Code = nhanhProduct.Code,
            Name = nhanhProduct.Name,
            ParentId = nhanhProduct.ParentId ?? 0,
            Description = "", // API search không trả về description
            Price = double.TryParse(nhanhProduct.Price, out var price) ? price : 0,
            Vat = int.TryParse(nhanhProduct.Vat, out var vat) ? vat : 0,
            Image = nhanhProduct.Image,
            Images = !string.IsNullOrEmpty(nhanhProduct.Image) ? [nhanhProduct.Image] : [],
            Status = nhanhProduct.Status,
            Weight = double.TryParse(nhanhProduct.ShippingWeight, out var weight) ? weight : 0,
            CategoryId = int.TryParse(nhanhProduct.CategoryId, out var categoryId) ? categoryId : 0,
            Length = float.TryParse(nhanhProduct.Length, out var length) ? length : null,
            Width = float.TryParse(nhanhProduct.Width, out var width) ? width : null,
            Height = float.TryParse(nhanhProduct.Height, out var height) ? height : null,
            CreatedDateTime = nhanhProduct.CreatedDateTime,
            Inventory = new NhanhProductInventoryDto
            {
                Available = nhanhProduct.Inventory?.Available ?? 0,
                Remain = nhanhProduct.Inventory?.Remain ?? 0,
                Shipping = nhanhProduct.Inventory?.Shipping ?? 0,
                Holding = nhanhProduct.Inventory?.Holding ?? 0,
                Damage = nhanhProduct.Inventory?.Damaged ?? 0
            },
            // Chuyển đổi attributes
            Attributes = nhanhProduct.Attributes?.SelectMany(attrDict =>
                attrDict.Values.Select(attr => new NhanhProductAttributeDto
                {
                    AttributeName = attr.AttributeName,
                    Id = int.TryParse(attr.Id, out var attrId) ? attrId : 0,
                    Name = attr.Name,
                    Content = attr.Value
                })).ToList() ?? new List<NhanhProductAttributeDto>()
        };

        // Sử dụng logic đồng bộ với categories đã có sẵn để tránh gọi API nhiều lần
        var result = await _nhanhHelper.SyncNhanhProductFromWebhookWithCategories(webhookDto, shopId, categories);

        if (!result.IsSuccess)
        {
            _log.Info($"{requestId} Không thể đồng bộ sản phẩm {nhanhProduct.IdNhanh}");
        }
        else
        {
            _log.Debug($"{requestId} Đã đồng bộ thành công sản phẩm {nhanhProduct.IdNhanh} - {nhanhProduct.Name}");
        }
    }

    /// <summary>
    /// Lấy tất cả parent products (sản phẩm cha có biến thể) từ Nhanh.vn
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="requestId">ID request để tracking log</param>
    /// <returns>Dictionary với key là parentId và value là parent product</returns>
    private async Task<Dictionary<int, NhanhProductDetailDto>> GetAllParentProducts(string shopId, string requestId)
    {
        var allParentProducts = new Dictionary<int, NhanhProductDetailDto>();

        try
        {
            int page = 1;
            int maxPages = 1000;

            while (page <= maxPages)
            {
                _log.Info($"{requestId} Đang lấy parent products trang {page} cho shop {shopId}");

                // Gọi API với retry mechanism để lấy các sản phẩm cha có biến thể
                var parentProductsResult = await ExecuteWithRetry(
                    () => _nhanhHelper.GetNhanhParentProductsAsync(shopId, page),
                    requestId,
                    $"GetNhanhParentProductsAsync cho shop {shopId}, trang {page}"
                );

                if (!parentProductsResult.IsSuccess)
                {
                    _log.Error($"{requestId} Lỗi khi lấy parent products trang {page} cho shop {shopId}. Chuyển sang trang tiếp theo.");

                    // Chuyển sang trang tiếp theo khi gặp lỗi
                    page++;
                    continue;
                }

                var response = parentProductsResult.Data;

                if (response == null || response.Products == null || response.Products.Count == 0)
                {
                    _log.Info($"{requestId} Không có parent products nào ở trang {page} cho shop {shopId}");
                    break;
                }

                _log.Info($"{requestId} Lấy được {response.Products.Count} parent products từ trang {page}/{response.TotalPages} cho shop {shopId}");

                // Thêm vào dictionary
                foreach (var productKvp in response.Products)
                {
                    var parentProduct = productKvp.Value;
                    if (!allParentProducts.ContainsKey(parentProduct.IdNhanh))
                    {
                        allParentProducts[parentProduct.IdNhanh] = parentProduct;
                    }
                }

                // Kiểm tra điều kiện dừng
                if (page >= response.TotalPages)
                {
                    _log.Info($"{requestId} Đã lấy hết tất cả {response.TotalPages} trang parent products cho shop {shopId}");
                    break;
                }

                page++;
                await Task.Delay(PARENT_PRODUCTS_DELAY_MS); // Delay để tránh rate limiting
            }
        }
        catch (Exception ex)
        {
            _log.Error($"{requestId} Lỗi khi lấy parent products cho shop {shopId}: {ex.Message}", ex);
        }

        return allParentProducts;
    }

    /// <summary>
    /// Đồng bộ một sản phẩm cụ thể với parent products cache
    /// </summary>
    /// <param name="nhanhProduct">Sản phẩm từ Nhanh.vn</param>
    /// <param name="shopId">ID của shop</param>
    /// <param name="categories">Danh sách categories</param>
    /// <param name="parentProductsCache">Cache của parent products</param>
    /// <param name="requestId">ID request để tracking log</param>
    private async Task SyncSingleProductWithParentCache(NhanhProductDetailDto nhanhProduct, string shopId, List<NhanhProductCategoryDto> categories, Dictionary<int, NhanhProductDetailDto> parentProductsCache, string requestId)
    {
        // Chuyển đổi từ NhanhProductDetailDto sang NhanhProductWebhookDto để sử dụng logic đã có
        var webhookDto = new NhanhProductWebhookDto
        {
            ProductId = nhanhProduct.IdNhanh,
            ParentId = nhanhProduct.ParentId ?? 0,
            Name = nhanhProduct.Name,
            Code = nhanhProduct.Code,
            Barcode = nhanhProduct.Barcode,
            Image = nhanhProduct.Image,
            Price = double.TryParse(nhanhProduct.Price, out var price) ? price : 0,
            Description = "", // NhanhProductDetailDto không có Description field
            Status = nhanhProduct.Status,
            CategoryId = int.TryParse(nhanhProduct.CategoryId, out var catId) ? catId : 0,
            BrandId = nhanhProduct.BrandId ?? 0,
            CreatedDateTime = nhanhProduct.CreatedDateTime,
            Attributes = nhanhProduct.Attributes?.SelectMany(attrDict =>
                attrDict.Values.Select(attr => new NhanhProductAttributeDto
                {
                    AttributeName = attr.AttributeName,
                    Id = int.TryParse(attr.Id, out var attrId) ? attrId : 0,
                    Name = attr.Name,
                    Content = attr.Value
                })).ToList() ?? new List<NhanhProductAttributeDto>()
        };

        // Sử dụng logic đồng bộ với categories và parent products cache
        var result = await _nhanhHelper.SyncNhanhProductFromWebhookWithParentCache(webhookDto, shopId, categories, parentProductsCache);

        if (!result.IsSuccess)
        {
            _log.Info($"{requestId} Không thể đồng bộ sản phẩm {nhanhProduct.IdNhanh}");
        }
        else
        {
            _log.Debug($"{requestId} Đã đồng bộ thành công sản phẩm {nhanhProduct.IdNhanh} - {nhanhProduct.Name}");
        }
    }
}
