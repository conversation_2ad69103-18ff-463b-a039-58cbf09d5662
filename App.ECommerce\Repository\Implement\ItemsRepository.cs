using App.Base.Repository;
using App.Base.Repository.Entities;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using log4net;

using MongoDB.Bson;
using MongoDB.Driver;

using OfficeOpenXml;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

using System.Linq;

namespace App.ECommerce.Repository.Implement;

public class ItemsRepository : BaseRepository, IItemsRepository
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(ItemsRepository));
    private readonly IMongoCollection<Items> _collectionItems;
    private readonly IMongoCollection<Variant> _collectionVariant;
    private readonly IMongoCollection<Entities.Category> _collectionCategory;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IWarehouseRepository _warehouseRepository;
    private readonly IShopRepository _shopRepository;

    public ItemsRepository(
        ICategoryRepository categoryRepository,
        IWarehouseRepository warehouseRepository,
        IShopRepository shopRepository
    ) : base()
    {
        _collectionItems = _database.GetCollection<Items>($"Items");
        _collectionVariant = _database.GetCollection<Variant>($"Variant");
        _collectionCategory = _database.GetCollection<Entities.Category>($"Category");

        var indexOptions = new CreateIndexOptions();
        var indexModelItems = new List<CreateIndexModel<Items>>()
        {
            new CreateIndexModel<Items>(Builders<Items>.IndexKeys.Ascending(item => item.ItemsId), indexOptions),
            new CreateIndexModel<Items>(Builders<Items>.IndexKeys.Ascending(item => item.ItemsCode), indexOptions),
            new CreateIndexModel<Items>(Builders<Items>.IndexKeys.Ascending(item => item.ItemsType), indexOptions),
            new CreateIndexModel<Items>(Builders<Items>.IndexKeys.Ascending(item => item.ItemsName), indexOptions),
        };
        _collectionItems.Indexes.CreateMany(indexModelItems);
        var indexModelVariant = new List<CreateIndexModel<Variant>>()
        {
            new CreateIndexModel<Variant>(Builders<Variant>.IndexKeys.Ascending(item => item.VariantId), indexOptions),
        };
        _collectionVariant.Indexes.CreateMany(indexModelVariant);

        _categoryRepository = categoryRepository;
        _warehouseRepository = warehouseRepository;
        _shopRepository = shopRepository;
    }

    //=== Items

    #region Items

    public Items CreateItems(Items item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.ItemsId = Guid.NewGuid().ToString();
        item.ItemsNameOrigin = item.ItemsName.NonUnicode().ToLower();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionItems.InsertOne(item);
        return item;
    }

    public async Task<List<Items>> CreateItemsAsync(IEnumerable<Items> items)
    {
        var itemsList = items.ToList();
        foreach (var item in itemsList)
        {
            ObjectId objectId = ObjectId.GenerateNewId();
            item.Id = new BsonObjectId(objectId).ToString();
            item.ItemsId = Guid.NewGuid().ToString();
            item.ItemsNameOrigin = item.ItemsName.NonUnicode().ToLower();
            item.Created = DateTimes.Now();
            item.Updated = DateTimes.Now();
        }
        await _collectionItems.InsertManyAsync(itemsList);
        return itemsList;
    }

    public Items RestoreItems(Items item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.ItemsId = (!string.IsNullOrEmpty(item.ItemsId) ? item.ItemsId : Guid.NewGuid().ToString());
        item.ItemsNameOrigin = item.ItemsName.NonUnicode().ToLower();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionItems.InsertOne(item);
        return item;
    }

    public Items DeleteItems(string itemsId)
    {
        return _collectionItems.FindOneAndDelete(item => item.ItemsId == itemsId);
    }

    public Items? FindByItemsId(string itemsId)
    {
        return _collectionItems.Find(item => item.ItemsId == itemsId).FirstOrDefault();
    }

    public Items? FindByExternalId(string externalId, SyncServiceEnum externalSource)
    {
        return _collectionItems.Find(item => item.ExternalId == externalId && item.ExternalSource == externalSource).FirstOrDefault();
    }

    public List<Items> FindByItemsCode(string itemsCode)
    {
        var filter = Builders<Items>.Filter.And(
            Builders<Items>.Filter.Where(p => p.ItemsCode == itemsCode)
        );
        return _collectionItems.Find(filter).ToList();
    }

    public List<Items> FindByItemsIds(string itemsIds)
    {
        var listIds = itemsIds.Split(',');
        var filter = Builders<Items>.Filter.In(x => x.ItemsId, listIds);
        return _collectionItems.Find(filter).ToList();
    }

    public List<Items> FindByItemsCodes(List<string> itemsCodes)
    {
        var filter = Builders<Items>.Filter.In(x => x.ItemsCode, itemsCodes);
        return _collectionItems.Find(filter).ToList();
    }

    public PagingResult<Items> ListItems(Paging paging, string? partnerId = null, string? shopId = null,
        string? categoryId = null)
    {
        PagingResult<Items> result = new PagingResult<Items>();
        FilterDefinition<Items> filterBuilders = Builders<Items>.Filter.And(
            // Builders<Items>.Filter.Where(p => status == null || p.Status == status),
            Builders<Items>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId),
            Builders<Items>.Filter.Where(p => shopId == null || p.ShopId == shopId),
            Builders<Items>.Filter.Or(
                Builders<Items>.Filter.Where(p => categoryId == null || (p.CategoryIds != null && p.CategoryIds.Contains(categoryId))),
                Builders<Items>.Filter.Where(p => categoryId == null)
            ),
            Builders<Items>.Filter.Or(
                Builders<Items>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Items>.Filter.Regex(x => x.ItemsNameOrigin,
                    new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(),
                        "i"))
            )
        );

        var query = _collectionItems.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public Items? UpdateItems(Items item)
    {
        Items _item = _collectionItems.Find(x => x.ItemsId == item.ItemsId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<Items>.Update
            .Set("ItemsId", item.ItemsId)
            .Set("ItemsCode", item.ItemsCode)
            .Set("PartnerId", item.PartnerId)
            .Set("ShopId", item.ShopId)
            .Set("ItemsType", item.ItemsType)
            .Set("CategoryIds", item.CategoryIds)
            .Set("ItemsName", item.ItemsName)
            .Set("ItemsNameOrigin", item.ItemsName.NonUnicode().ToLower())
            .Set("IsTop", item.IsTop)
            .Set("ItemsPosition", item.ItemsPosition)
            .Set("ItemsInfo", item.ItemsInfo)
            .Set("Images", item.Images)
            .Set("Sold", item.Sold)
            // Variant
            .Set("IsVariant", item.IsVariant)
            .Set("VariantImage", item.VariantImage)
            .Set("VariantNameOne", item.VariantNameOne)
            .Set("VariantValueOne", item.VariantValueOne)
            .Set("VariantNameTwo", item.VariantNameTwo)
            .Set("VariantValueTwo", item.VariantValueTwo)
            .Set("VariantNameThree", item.VariantNameThree)
            .Set("VariantValueThree", item.VariantValueThree)
            .Set("PriceCapital", item.PriceCapital)
            .Set("PriceReal", item.PriceReal)
            .Set("Price", item.Price)
            .Set("Quantity", item.Quantity)
            .Set("QuantityPurchase", item.QuantityPurchase)
            // Transport
            .Set("WarehouseId", item.WarehouseId)
            .Set("ItemsWeight", item.ItemsWeight)
            .Set("ItemsLength", item.ItemsLength)
            .Set("ItemsWidth", item.ItemsWidth)
            .Set("ItemsHeight", item.ItemsHeight)
            .Set("SeoTags", item.SeoTags)
            .Set("TypePublish", item.TypePublish)
            .Set("Status", item.Status)
            .Set("ExtraItemOptionGroups", item.ExtraItemOptionGroups)
            .Set("TransportType", item.TransportType)
            .Set("CustomTaxRate", item.CustomTaxRate)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Items>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Items> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionItems.FindOneAndUpdate(filter, update, options);
    }

    public long TotalItemsIds(string itemsIds)
    {
        var listIds = itemsIds.Split(',');
        FilterDefinition<Items> filterBuilders = Builders<Items>.Filter.And(
            Builders<Items>.Filter.In(x => x.ItemsId, listIds)
        );
        return _collectionItems.CountDocuments(filterBuilders);
    }

    // public long TotalItems(string? partnerId = null, string? shopId = null, string? categoryId = null)
    // {
    //     FilterDefinition<Items> filterBuilders = Builders<Items>.Filter.And(
    //         Builders<Items>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId),
    //         Builders<Items>.Filter.Where(p => shopId == null || p.ShopId == shopId),
    //         Builders<Items>.Filter.Where(p => !p.IsVariant), // Chỉ đếm items chính, không đếm biến thể
    //         Builders<Items>.Filter.Or(
    //             Builders<Items>.Filter.Where(p => categoryId == null || (p.CategoryIds != null && p.CategoryIds.Contains(categoryId))),
    //             Builders<Items>.Filter.Where(p => categoryId == null)
    //         )
    //     );
    //     return _collectionItems.CountDocuments(filterBuilders);
    // }

    public long TotalItems(string? partnerId = null, string? shopId = null, string? categoryId = null)
    {
        var filterBuilder = Builders<Items>.Filter;
        var filter = filterBuilder.Empty;

        if (!string.IsNullOrEmpty(partnerId))
            filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.PartnerId, partnerId));
        if (!string.IsNullOrEmpty(shopId))
            filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.ShopId, shopId));
        if (!string.IsNullOrEmpty(categoryId))
            filter = filterBuilder.And(filter, filterBuilder.AnyEq(x => x.CategoryIds, categoryId));

        // Group theo ItemsCode để không bị trùng biến thể
        var group = new BsonDocument
        {
            { "_id", "$ItemsCode" }
        };

        var pipeline = new[]
        {
            new BsonDocument { { "$match", filter.Render(_collectionItems.DocumentSerializer, _collectionItems.Settings.SerializerRegistry) } },
            new BsonDocument { { "$group", group } }
        };

        var count = _collectionItems.Aggregate<BsonDocument>(pipeline).ToList().Count;

        return count;
    }

    public async Task<Items> UpdateImages(string shopId, string itemsCode, List<MediaInfo> images)
    {
        var item = _collectionItems.Find(x => x.ShopId == shopId && x.ItemsCode == itemsCode).FirstOrDefault();
        if (item == null) return null;

        // Lấy danh sách ảnh hiện tại
        var currentImages = item.Images ?? new List<MediaInfo>();

        // Thêm ảnh mới vào danh sách hiện có
        var updatedImages = new List<MediaInfo>(currentImages);
        updatedImages.AddRange(images);

        var update = Builders<Items>.Update
            .Set("Images", updatedImages)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Items>.Filter.Eq("Id", item.Id);
        var options = new FindOneAndUpdateOptions<Items> { IsUpsert = false, ReturnDocument = ReturnDocument.After };
        return await _collectionItems.FindOneAndUpdateAsync(filter, update, options);
    }

    #endregion Items ./


    //=== Variant

    #region Variant

    public Variant CreateVariant(Variant item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.VariantId = Guid.NewGuid().ToString();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionVariant.InsertOne(item);
        return item;
    }

    public Variant RestoreVariant(Variant item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.VariantId = (!string.IsNullOrEmpty(item.VariantId) ? item.VariantId : Guid.NewGuid().ToString());
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionVariant.InsertOne(item);
        return item;
    }

    public Variant DeleteVariant(string variantId)
    {
        return _collectionVariant.FindOneAndDelete(item => item.VariantId == variantId);
    }

    public Variant? FindByVariantId(string variantId)
    {
        return _collectionVariant.Find(item => item.VariantId == variantId).FirstOrDefault();
    }

    public List<Variant> FindByVariantIds(string variantIds)
    {
        var listIds = variantIds.Split(',');
        var filter = Builders<Variant>.Filter.In(x => x.VariantId, listIds);
        return _collectionVariant.Find(filter).ToList();
    }

    public PagingResult<Variant> ListVariant(Paging paging, string? itemsId = null)
    {
        PagingResult<Variant> result = new PagingResult<Variant>();
        FilterDefinition<Variant> filterBuilders = Builders<Variant>.Filter.And(
            Builders<Variant>.Filter.Where(p => p.ItemsId == itemsId),
            //Builders<Variant>.Filter.Where(p => active == null || p.Active == active),
            Builders<Variant>.Filter.Or(
                Builders<Variant>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Variant>.Filter.Regex(x => x.VariantNameOne,
                    new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );

        var query = _collectionVariant.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public Variant? UpdateVariant(Variant item)
    {
        Variant _item = _collectionVariant.Find(x => x.VariantId == item.VariantId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<Variant>.Update
            .Set("VariantId", item.VariantId)
            .Set("ItemsId", item.ItemsId)
            .Set("OneName", item.VariantNameOne)
            .Set("OneValue", item.VariantValueOne)
            .Set("TwoName", item.VariantNameTwo)
            .Set("TwoValue", item.VariantValueTwo)
            .Set("ThreeName", item.VariantNameThree)
            .Set("ThreeValue", item.VariantValueThree)
            .Set("PriceCapital", item.PriceCapital)
            .Set("PriceReal", item.PriceReal)
            .Set("Price", item.Price)
            .Set("Quantity", item.Quantity)
            .Set("QuantityPurchase", item.QuantityPurchase)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Variant>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Variant> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionVariant.FindOneAndUpdate(filter, update, options);
    }

    public long TotalVariant(string itemsId)
    {
        FilterDefinition<Variant> filterBuilders = Builders<Variant>.Filter.And(
            Builders<Variant>.Filter.Where(p => p.ItemsId == itemsId)
        );
        return _collectionVariant.CountDocuments(filterBuilders);
    }

    #endregion Variant ./


    //=== Items-Variant

    #region Items-Variant

    public PagingResult<object> ListQueryLookupVariant(Paging paging)
    {
        PagingResult<object> result = new PagingResult<object> { Result = new List<object>(), Total = 0 };
        var pipeline = lookupVariant($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(),
            (paging.PageIndex * paging.PageSize), paging.PageSize);
        var cursor = _collectionItems.Aggregate<PagingQuery<object>>(pipeline);
        var first = cursor.SingleOrDefault();
        Logs.debug($"{Newtonsoft.Json.JsonConvert.SerializeObject(first, JsonSettings.SettingForNewtonsoftPretty)}");
        result.Result = first?.Result ?? new List<object>();
        result.Total = first?.Total ?? 0;
        return result;
    }

    #endregion Items-Variant ./

    //=== Function   

    #region Function

    public ItemsGroupBy? GroupByVariant(string itemsCode)
    {
        // Sử dụng Find thay vì aggregation để tránh BSON size limit
        var filterBuilders = Builders<Items>.Filter.And(
            Builders<Items>.Filter.Where(x => x.ItemsCode == itemsCode)
        );

        var allItems = _collectionItems.Find(filterBuilders)
            .Sort(Builders<Items>.Sort.Ascending(x => x.ItemsPosition).Descending(x => x.Created))
            .ToList();

        if (allItems == null || allItems.Count == 0)
            return null;

        var firstItem = allItems.First();

        var result = new ItemsGroupBy
        {
            ItemsId = firstItem.ItemsId,
            ItemsCode = firstItem.ItemsCode,
            PartnerId = firstItem.PartnerId,
            ShopId = firstItem.ShopId,
            ItemsType = firstItem.ItemsType,
            CategoryIds = firstItem.CategoryIds,
            ItemsName = firstItem.ItemsName,
            ItemsNameOrigin = firstItem.ItemsNameOrigin,
            Quantity = firstItem.Quantity,
            QuantityPurchase = firstItem.QuantityPurchase,
            IsTop = firstItem.IsTop,
            IsShow = firstItem.IsShow,
            ItemsPosition = firstItem.ItemsPosition,
            ItemsInfo = firstItem.ItemsInfo,
            Images = firstItem.Images,
            Sold = firstItem.Sold,
            IsVariant = firstItem.IsVariant,
            ListVariant = allItems.Cast<VariantBase>().ToList(),
            WarehouseId = firstItem.WarehouseId,
            VariantNameOne = firstItem.VariantNameOne,
            VariantValueOne = firstItem.VariantValueOne,
            VariantNameTwo = firstItem.VariantNameTwo,
            VariantValueTwo = firstItem.VariantValueTwo,
            VariantNameThree = firstItem.VariantNameThree,
            VariantValueThree = firstItem.VariantValueThree,
            ItemsWeight = firstItem.ItemsWeight,
            ItemsLength = firstItem.ItemsLength,
            ItemsWidth = firstItem.ItemsWidth,
            ItemsHeight = firstItem.ItemsHeight,
            SeoTags = firstItem.SeoTags,
            SellOver = firstItem.SellOver,
            TypePublish = firstItem.TypePublish,
            Status = firstItem.Status,
            ExtraItemOptionGroups = firstItem.ExtraItemOptionGroups,
            TransportType = firstItem.TransportType,
            CustomTaxRate = firstItem.CustomTaxRate
        };

        var shop = _shopRepository.FindByShopId(result.ShopId);
        if (shop != null && result.CustomTaxRate == null)
        {
            result.CustomTaxRate = shop.DefaultTaxRate;
        }

        return result;
    }

    public PagingResult<ItemsGroupBy> ListQueryGroupByVariant(Paging paging, string? shopId = null,
        string? categoryId = null, string? subCategoryId = null, TypeItems? itemsType = null)
    {
        var shop = _shopRepository.FindByShopId(shopId);
        if (shop == null)
            return new PagingResult<ItemsGroupBy> { Result = new List<ItemsGroupBy>(), Total = 0 };

        PagingResult<ItemsGroupBy> result = new PagingResult<ItemsGroupBy>
        {
            Result = new List<ItemsGroupBy>(),
            Total = 0
        };

        try
        {
            // Tối ưu hóa aggregation pipeline để tránh lỗi BSON size limit
            var filterBuilders = Builders<Items>.Filter.And(
                Builders<Items>.Filter.Where(x => shopId == null || x.ShopId == shopId),
                Builders<Items>.Filter.Where(x => itemsType == null || x.ItemsType == itemsType),
                Builders<Items>.Filter.Where(x => categoryId == null || x.CategoryIds.Contains(categoryId)),
                Builders<Items>.Filter.Where(x => subCategoryId == null || x.CategoryIds.Contains(subCategoryId)),
                Builders<Items>.Filter.Or(
                    Builders<Items>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                    Builders<Items>.Filter.Regex(x => x.ItemsNameOrigin,
                        new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(),
                            "i"))
                ));

            var allItems = _collectionItems.Find(filterBuilders)
                .Sort(Builders<Items>.Sort.Ascending(x => x.ItemsPosition).Descending(x => x.Created))
                .ToList();

            // Nhóm items theo ItemsCode
            Dictionary<string, List<Items>> itemsDict = new Dictionary<string, List<Items>>();
            foreach (var item in allItems)
            {
                if (!itemsDict.ContainsKey(item.ItemsCode))
                {
                    itemsDict[item.ItemsCode] = new List<Items>();
                }
                itemsDict[item.ItemsCode].Add(item);
            }

            var distinctItemsCodes = itemsDict.Keys.ToList();
            result.Total = distinctItemsCodes.Count;

            var pagedItemsCodes = distinctItemsCodes
                .Skip(paging.PageIndex * paging.PageSize)
                .Take(paging.PageSize)
                .ToList();

            var resultList = new List<ItemsGroupBy>();

            foreach (var itemsCode in pagedItemsCodes)
            {
                if (itemsDict.TryGetValue(itemsCode, out var items) && items.Count > 0)
                {
                    var firstItem = items.First();

                    var groupItem = new ItemsGroupBy
                    {
                        ItemsId = firstItem.ItemsId,
                        ItemsCode = firstItem.ItemsCode,
                        PartnerId = firstItem.PartnerId,
                        ShopId = firstItem.ShopId,
                        ItemsType = firstItem.ItemsType,
                        CategoryIds = firstItem.CategoryIds,
                        ItemsName = firstItem.ItemsName,
                        ItemsNameOrigin = firstItem.ItemsNameOrigin,
                        IsTop = firstItem.IsTop,
                        IsShow = firstItem.IsShow,
                        ItemsPosition = firstItem.ItemsPosition,
                        ItemsInfo = firstItem.ItemsInfo,
                        Images = firstItem.Images,
                        Sold = firstItem.Sold,
                        IsVariant = firstItem.IsVariant,
                        ListVariant = items.Cast<VariantBase>().ToList(),
                        WarehouseId = firstItem.WarehouseId,
                        ItemsWeight = firstItem.ItemsWeight,
                        ItemsLength = firstItem.ItemsLength,
                        ItemsWidth = firstItem.ItemsWidth,
                        ItemsHeight = firstItem.ItemsHeight,
                        SeoTags = firstItem.SeoTags,
                        TypePublish = firstItem.TypePublish,
                        Status = firstItem.Status,
                        ExtraItemOptionGroups = firstItem.ExtraItemOptionGroups,
                        TransportType = firstItem.TransportType,
                        CustomTaxRate = firstItem.CustomTaxRate
                    };

                    resultList.Add(groupItem);
                }
            }

            result.Result = resultList;

            foreach (var item in result.Result)
            {
                if (item.CustomTaxRate == null)
                    item.CustomTaxRate = shop.DefaultTaxRate;
            }

            return result;
        }
        catch (Exception ex)
        {
            // Log lỗi và trả về kết quả rỗng
            Logs.debug($"Error in ListQueryGroupByVariant: {ex.Message}");
            return result;
        }
    }

    public PagingResult<ItemsGroupBy> ListQueryGroupByVariantExtend(bool isUser, Paging paging, ItemFilterDto model)
    {
        PagingResult<ItemsGroupBy> result = new PagingResult<ItemsGroupBy>
        { Result = new List<ItemsGroupBy>(), Total = 0 };

        try
        {
            var filterBuilder = Builders<Items>.Filter;
            var filterItems = filterBuilder.Eq(x => x.ShopId, model.ShopId);

            if (model.ItemsType.HasValue)
                filterItems = filterBuilder.And(filterItems, filterBuilder.Eq(x => x.ItemsType, model.ItemsType.Value));

            if (model.Publish.HasValue)
                filterItems = filterBuilder.And(filterItems, filterBuilder.Eq(x => x.TypePublish, model.Publish.Value));

            if (!string.IsNullOrEmpty(paging.Search))
            {
                filterItems = filterBuilder.And(
                    filterItems,
                    filterBuilder.Regex(x => x.ItemsNameOrigin,
                        new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(), "i"))
                );
            }

            if (!string.IsNullOrEmpty(model.CategoryId))
                filterItems = filterBuilder.And(filterItems, Builders<Items>.Filter.Eq("CategoryIds", model.CategoryId));

            if (model.MinPrice.HasValue)
                filterItems = filterBuilder.And(filterItems, filterBuilder.Gte(x => x.Price, model.MinPrice.Value));
            if (model.MaxPrice.HasValue)
                filterItems = filterBuilder.And(filterItems, filterBuilder.Lte(x => x.Price, model.MaxPrice.Value));

            Dictionary<string, List<Items>> itemsDict = new Dictionary<string, List<Items>>();

            var projection = Builders<Items>.Projection.Exclude(x => x.ItemsInfo)
                .Exclude(x => x.WarehouseId)
                .Exclude(x => x.ItemsWeight)
                .Exclude(x => x.ItemsLength)
                .Exclude(x => x.SeoTags)
                .Exclude(x => x.TransportType)
                .Exclude(x => x.ExtraItemOptionGroups);

            var allItems = _collectionItems.Find(filterItems)
                .Project<Items>(projection)
                .Sort(Builders<Items>.Sort.Ascending(x => x.ItemsCode).Ascending(x => x.ItemsPosition))
                .ToList();

            var publishedCategoryIds = _collectionCategory.Find(x => x.Publish == TypeCategoryPublish.Publish)
                                                        .Project(x => x.CategoryId)
                                                        .ToList();

            foreach (var item in allItems)
            {
                if (isUser && item.CategoryIds != null) {
                    var categoryIds = item.CategoryIds.Where(x => publishedCategoryIds.Contains(x)).ToList();
                    if (categoryIds.Count > 0)
                        item.CategoryIds = categoryIds;
                    else
                        continue;
                }

                if (!itemsDict.ContainsKey(item.ItemsCode))
                {
                    itemsDict[item.ItemsCode] = new List<Items>();
                }
                itemsDict[item.ItemsCode].Add(item);
            }

            var distinctItemsCodes = itemsDict.Keys.ToList();

            if (model.ShowInHome.HasValue)
            {
                var filteredItemsCodes = new List<string>();

                foreach (var itemsCode in distinctItemsCodes)
                {
                    if (itemsDict.TryGetValue(itemsCode, out var items))
                    {
                        var firstItem = items.FirstOrDefault();
                        if (firstItem == null) continue;

                        var categoryIds = firstItem.CategoryIds ?? new List<string>();
                        var categories = _collectionCategory.Find(x => categoryIds.Contains(x.CategoryId)).ToList();

                        var hasHomeCategory = categories.Any(c => c.IsHome == true);

                        if ((model.ShowInHome.Value && hasHomeCategory) || (!model.ShowInHome.Value && !hasHomeCategory))
                        {
                            filteredItemsCodes.Add(itemsCode);
                        }
                    }
                }

                distinctItemsCodes = filteredItemsCodes;
            }

            result.Total = distinctItemsCodes.Count;

            var sortedItemsCodes = new List<string>();

            var itemsWithSortInfo = new List<(string ItemsCode, int Position, long Price, long PriceReal, long PriceCapital, DateTime Created)>();
            foreach (var itemsCode in distinctItemsCodes)
            {
                if (itemsDict.TryGetValue(itemsCode, out var items) && items.Count > 0)
                {
                    var firstItem = items.First();
                    itemsWithSortInfo.Add((
                        itemsCode,
                        firstItem.ItemsPosition,
                        firstItem.Price ?? 0,
                        firstItem.PriceReal ?? 0,
                        firstItem.PriceCapital ?? 0,
                        firstItem.Created
                    ));
                }
            }

            switch (paging.NameType)
            {
                case TypeSortName.Price:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => paging.SortType == TypeSort.asc ? x.Price : -x.Price)
                        .ThenBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
                case TypeSortName.PriceReal:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => paging.SortType == TypeSort.asc ? x.PriceReal : -x.PriceReal)
                        .ThenBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
                case TypeSortName.PriceCapital:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => paging.SortType == TypeSort.asc ? x.PriceCapital : -x.PriceCapital)
                        .ThenBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
                case TypeSortName.Created:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => paging.SortType == TypeSort.asc ? x.Created.Ticks : -x.Created.Ticks)
                        .ThenBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
                case TypeSortName.Updated:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
                default:
                    sortedItemsCodes = itemsWithSortInfo
                        .OrderBy(x => x.Position)
                        .Select(x => x.ItemsCode)
                        .ToList();
                    break;
            }

            var pagedItemsCodes = sortedItemsCodes
                .Skip(paging.PageIndex * paging.PageSize)
                .Take(paging.PageSize)
                .ToList();

            var resultList = new List<ItemsGroupBy>();

            foreach (var itemsCode in pagedItemsCodes)
            {
                if (itemsDict.TryGetValue(itemsCode, out var items) && items.Count > 0)
                {
                    var firstItem = items.First();

                    var groupItem = new ItemsGroupBy
                    {
                        ItemsId = firstItem.ItemsId,
                        ItemsCode = firstItem.ItemsCode,
                        PartnerId = firstItem.PartnerId,
                        ShopId = firstItem.ShopId,
                        ItemsType = firstItem.ItemsType,
                        CategoryIds = firstItem.CategoryIds,
                        ItemsName = firstItem.ItemsName,
                        ItemsNameOrigin = firstItem.ItemsNameOrigin,
                        IsTop = firstItem.IsTop,
                        IsShow = firstItem.IsShow,
                        ItemsPosition = firstItem.ItemsPosition,
                        Images = firstItem.Images,
                        Quantity = firstItem.Quantity,
                        QuantityPurchase = firstItem.QuantityPurchase,
                        Sold = firstItem.Sold,
                        IsVariant = firstItem.IsVariant,
                        ListVariant = items.Cast<VariantBase>().ToList(),
                        TypePublish = firstItem.TypePublish,
                        Status = firstItem.Status
                    };

                    resultList.Add(groupItem);
                }
            }

            result.Result = resultList;
        }
        catch (Exception ex)
        {
            _log.Error($"Error in ListQueryGroupByVariantExtend: {ex.Message}", ex);
        }

        return result;
    }

    #endregion Function ./

    //=== Order
    public void UpdatePurchasedNumber(List<ItemsOrder> listItemsOrder,
        TypeOrderStatus statusOrder = TypeOrderStatus.Success)
    {
        foreach (var item in listItemsOrder)
        {
            var oldProduct = _collectionItems.Find(x => x.ItemsId == item.ItemsId).FirstOrDefault();

            if (statusOrder == TypeOrderStatus.Success)
            {
                var update = Builders<Items>.Update
                    .Set("Quantity",
                        (oldProduct.Quantity - item.Quantity) >= 0 ? (oldProduct.Quantity - item.Quantity) : 0)
                    .Set("Sold", oldProduct.Sold + item.Quantity);
                _collectionItems.FindOneAndUpdate(x => x.Id == oldProduct.Id, update);
            }
            else if (statusOrder == TypeOrderStatus.Failed || statusOrder == TypeOrderStatus.Refund)
            {
                var update = Builders<Items>.Update
                    .Set("Quantity", oldProduct.Quantity + item.Quantity)
                    .Set("Sold",
                        (oldProduct.Sold - item.Quantity) >= 0
                            ? (oldProduct.Sold - item.Quantity)
                            : 0);
                _collectionItems.FindOneAndUpdate(x => x.Id == oldProduct.Id, update);
            }
        }
    }

    //=== Excel

    #region Import-Export-Excel

    public async Task<byte[]> ExportItemsTemplate(string shopId, TypeItems type)
    {
        List<CategoryDto> categories = await _categoryRepository.GetListCategoryByTypeItem(shopId, type);
        var listCategory = new List<string>();
        listCategory.AddRange(categories.Select(c => c.CategoryDisplayName));

        var columns = type == TypeItems.Product ? ExportConst.HEADER_TEMPLATE_PRODUCT : ExportConst.HEADER_TEMPLATE_SERVICE;

        using (var package = new ExcelPackage())
        {
            // Tạo sheet ẩn chứa danh sách categories
            var hiddenSheet = package.Workbook.Worksheets.Add("Categories");
            hiddenSheet.Hidden = OfficeOpenXml.eWorkSheetHidden.VeryHidden;

            // Thêm danh sách categories vào sheet ẩn
            for (int i = 0; i < listCategory.Count; i++)
            {
                hiddenSheet.Cells[i + 1, 1].Value = listCategory[i];
            }

            var worksheet = package.Workbook.Worksheets.Add("ProductTemplate");

            for (int i = 0; i < columns.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = columns[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            var sampleRow = type == TypeItems.Product
                ? new object[]
                {
                    "Áo thun nam",
                    "",
                    "",
                    "",
                    100000, 120000, 80000, 50, 0.5, 30, 20, 5,
                    "Màu sắc", "Đỏ", "Kích thước", "M", "", "",
                    "https://example.com/image1.jpg;https://example.com/image2.jpg",
                    "Mô tả sản phẩm...", 0, 0, false, true
                }
                : new object[]
                {
                    "Dịch vụ tư vấn",
                    "",
                    "",
                    "",
                    500000, 600000, 400000, 10,
                    "https://example.com/image1.jpg",
                    "Mô tả dịch vụ...", 0, 0, false, true
                };

            for (int i = 0; i < sampleRow.Length; i++)
            {
                worksheet.Cells[2, i + 1].Value = sampleRow[i];
            }

            // Áp dụng style header
            using (var range = worksheet.Cells[1, 1, 1, columns.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Font.Name = "Times New Roman";
                range.Style.Font.Size = 13;
                range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            }

            // Áp dụng border + font cho toàn bộ bảng
            using (var range = worksheet.Cells[1, 1, 2, columns.Length])
            {
                range.Style.Font.Name = "Times New Roman";
                range.Style.Font.Size = 13;

                range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            }

            // Sử dụng tham chiếu đến sheet ẩn cho validation
            var categoryValidation = worksheet.DataValidations.AddListValidation($"B2:B1000");
            categoryValidation.ShowErrorMessage = true;
            categoryValidation.ErrorTitle = "Lỗi nhập liệu";
            categoryValidation.Error = "Vui lòng chọn danh mục từ danh sách.";
            categoryValidation.Formula.ExcelFormula = $"Categories!$A$1:$A${listCategory.Count}";

            var categoryValidation2 = worksheet.DataValidations.AddListValidation($"C2:C1000");
            categoryValidation2.ShowErrorMessage = true;
            categoryValidation2.ErrorTitle = "Lỗi nhập liệu";
            categoryValidation2.Error = "Vui lòng chọn danh mục từ danh sách.";
            categoryValidation2.Formula.ExcelFormula = $"Categories!$A$1:$A${listCategory.Count}";

            var categoryValidation3 = worksheet.DataValidations.AddListValidation($"D2:D1000");
            categoryValidation3.ShowErrorMessage = true;
            categoryValidation3.ErrorTitle = "Lỗi nhập liệu";
            categoryValidation3.Error = "Vui lòng chọn danh mục từ danh sách.";
            categoryValidation3.Formula.ExcelFormula = $"Categories!$A$1:$A${listCategory.Count}";

            int featuredColumnIndex = columns.Length - 2;
            var featuredValidation = worksheet.DataValidations.AddListValidation($"W2:W100".Replace("W", ((char)('A' + featuredColumnIndex)).ToString()));
            featuredValidation.Formula.Values.Add("True");
            featuredValidation.Formula.Values.Add("False");
            featuredValidation.ShowErrorMessage = true;
            featuredValidation.ErrorTitle = "Lỗi nhập liệu";
            featuredValidation.Error = "Vui lòng chọn True hoặc False.";

            int visibleColumnIndex = columns.Length - 1;
            var visibleValidation = worksheet.DataValidations.AddListValidation($"X2:X100".Replace("X", ((char)('A' + visibleColumnIndex)).ToString()));
            visibleValidation.Formula.Values.Add("True");
            visibleValidation.Formula.Values.Add("False");
            visibleValidation.ShowErrorMessage = true;
            visibleValidation.ErrorTitle = "Lỗi nhập liệu";
            featuredValidation.Error = "Vui lòng chọn True hoặc False.";

            // Thêm validation cho giá bán (cột 5)
            var priceValidation = worksheet.DataValidations.AddDecimalValidation($"E2:E1000");
            priceValidation.Operator = OfficeOpenXml.DataValidation.ExcelDataValidationOperator.greaterThan;
            priceValidation.Formula.Value = 0;
            priceValidation.ShowErrorMessage = true;
            priceValidation.ErrorTitle = "Lỗi nhập liệu";
            priceValidation.Error = "Giá bán là bắt buộc và phải lớn hơn 0";
            priceValidation.ShowInputMessage = true;
            priceValidation.PromptTitle = "Nhập giá bán";
            priceValidation.Prompt = "Vui lòng nhập giá bán (phải lớn hơn 0)";

            // Thêm validation cho giá gốc (cột 6)
            var priceCapitalValidation = worksheet.DataValidations.AddDecimalValidation($"F2:F1000");
            priceCapitalValidation.Operator = OfficeOpenXml.DataValidation.ExcelDataValidationOperator.greaterThan;
            priceCapitalValidation.Formula.Value = 0;
            priceCapitalValidation.ShowErrorMessage = true;
            priceCapitalValidation.ErrorTitle = "Lỗi nhập liệu";
            priceCapitalValidation.Error = "Giá gốc là bắt buộc và phải lớn hơn 0";
            priceCapitalValidation.ShowInputMessage = true;
            priceCapitalValidation.PromptTitle = "Nhập giá gốc";
            priceCapitalValidation.Prompt = "Vui lòng nhập giá gốc (phải lớn hơn 0)";

            // Thêm validation cho số lượng (cột 8)
            var quantityValidation = worksheet.DataValidations.AddIntegerValidation($"H2:H1000");
            quantityValidation.Operator = OfficeOpenXml.DataValidation.ExcelDataValidationOperator.greaterThanOrEqual;
            quantityValidation.Formula.Value = 0;
            quantityValidation.ShowErrorMessage = true;
            quantityValidation.ErrorTitle = "Lỗi nhập liệu";
            quantityValidation.Error = "Số lượng là bắt buộc và phải lớn hơn hoặc bằng 0";
            quantityValidation.ShowInputMessage = true;
            quantityValidation.PromptTitle = "Nhập số lượng";
            quantityValidation.Prompt = "Vui lòng nhập số lượng (phải lớn hơn hoặc bằng 0)";

            // Thêm protection cho worksheet
            worksheet.Protection.AllowDeleteRows = false;
            worksheet.Protection.AllowDeleteColumns = false;
            worksheet.Protection.AllowFormatCells = true;
            worksheet.Protection.AllowInsertColumns = false;
            worksheet.Protection.AllowInsertRows = false;
            // worksheet.Protection.IsProtected = true;

            for (int i = 1; i <= columns.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            var excelBytes = package.GetAsByteArray();
            return await Task.FromResult(excelBytes);
        }
    }

    public async Task<byte[]> ExportItemsToExcel(List<ItemsGroupByDto> items, TypeItems type, string shopId)
    {
        List<CategoryDto> categories = await _categoryRepository.GetListCategoryByTypeItem(shopId, type);
        Dictionary<string, string> categoryMap = categories.ToDictionary(c => c.CategoryId, c => c.CategoryDisplayName);

        using (var package = new ExcelPackage())
        {
            var worksheet = package.Workbook.Worksheets.Add(type == TypeItems.Product ? "Products" : "Services");

            var headers = type == TypeItems.Product
                ? ExportConst.HEADER_EXPPORT_PRODUCT
                : ExportConst.HEADER_EXPPORT_SERVICE;

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Font.Name = "Times New Roman";
            headerRange.Style.Font.Size = 13;
            headerRange.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            headerRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            headerRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            headerRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            headerRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

            int row = 2;
            int sequenceNumber = 1;
            foreach (var item in items)
            {
                if (item.IsVariant && item.ListVariant != null)
                {
                    foreach (var variant in item.ListVariant)
                    {
                        WriteItemRow(worksheet, row, item, variant, type, categoryMap, sequenceNumber);
                        row++;
                    }
                }
                else
                {
                    WriteItemRow(worksheet, row, item, null, type, categoryMap, sequenceNumber);
                    row++;
                }
                sequenceNumber++;
            }

            int total = type == TypeItems.Product ? (items.Count + 2) : (items.Count + 1);

            // Áp dụng style cho toàn bộ bảng
            var dataRange = worksheet.Cells[1, 1, total, headers.Length];
            dataRange.Style.Font.Name = "Times New Roman";
            dataRange.Style.Font.Size = 13;
            dataRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            dataRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            dataRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            dataRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

            worksheet.Cells[2, 1, total, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 2, total, 2].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
            worksheet.Cells[2, 3, total, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
            worksheet.Cells[2, 4, total, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 5, total, 5].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 6, total, 6].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 7, total, 7].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 8, total, 8].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 9, total, 9].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 10, total, 10].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 11, total, 11].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 12, total, 12].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 13, total, 13].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 14, total, 14].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 15, total, 15].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 16, total, 16].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 17, total, 17].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 18, total, 18].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 19, total, 19].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 20, total, 20].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 21, total, 21].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 22, total, 22].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 23, total, 23].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[2, 23, total, 23].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;

            for (int i = 1; i <= headers.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            return package.GetAsByteArray();
        }
    }

    private void WriteItemRow(ExcelWorksheet worksheet, int row, ItemsGroupByDto item, VariantBase? variant, TypeItems type, Dictionary<string, string> categoryMap, int sequenceNumber)
    {

        if (type == TypeItems.Product)
        {
            worksheet.Cells[row, 1].Value = sequenceNumber;
            worksheet.Cells[row, 2].Value = item.ItemsName;
            worksheet.Cells[row, 3].Value = string.Join(",", item.CategoryIds.Select(id => categoryMap.GetValueOrDefault(id, "")));
            worksheet.Cells[row, 4].Value = variant?.Price ?? item.Price;
            worksheet.Cells[row, 5].Value = variant?.PriceReal ?? item.PriceReal;
            worksheet.Cells[row, 6].Value = variant?.PriceCapital ?? item.PriceCapital;
            worksheet.Cells[row, 7].Value = variant?.Quantity ?? item.Quantity;
            worksheet.Cells[row, 8].Value = item.WarehouseId;
            worksheet.Cells[row, 9].Value = item.ItemsWeight;
            worksheet.Cells[row, 10].Value = item.ItemsLength;
            worksheet.Cells[row, 11].Value = item.ItemsWidth;
            worksheet.Cells[row, 12].Value = item.ItemsHeight;
            worksheet.Cells[row, 13].Value = variant?.VariantNameOne ?? "";
            worksheet.Cells[row, 14].Value = variant?.VariantValueOne ?? "";
            worksheet.Cells[row, 15].Value = variant?.VariantNameTwo ?? "";
            worksheet.Cells[row, 16].Value = variant?.VariantValueTwo ?? "";
            worksheet.Cells[row, 17].Value = variant?.VariantNameThree ?? "";
            worksheet.Cells[row, 18].Value = variant?.VariantValueThree ?? "";
            worksheet.Cells[row, 19].Value = item.QuantityPurchase;
            worksheet.Cells[row, 20].Value = item.ItemsPosition;
            worksheet.Cells[row, 21].Value = item.TypePublish == TypePublish.Publish ? "TRUE" : "FALSE";
            worksheet.Cells[row, 22].Value = item.IsTop.ToString().ToUpper();
            worksheet.Cells[row, 23].Value = string.Join(";", item.Images?.Select(img => img.Link) ?? new List<string>());
        }
        else
        {
            worksheet.Cells[row, 1].Value = sequenceNumber;
            worksheet.Cells[row, 2].Value = item.ItemsName;
            worksheet.Cells[row, 3].Value = string.Join(",", item.CategoryIds.Select(id => categoryMap.GetValueOrDefault(id, "")));
            worksheet.Cells[row, 4].Value = variant?.Price ?? item.Price;
            worksheet.Cells[row, 5].Value = variant?.PriceReal ?? item.PriceReal;
            worksheet.Cells[row, 6].Value = variant?.PriceCapital ?? item.PriceCapital;
            worksheet.Cells[row, 7].Value = variant?.Quantity ?? item.Quantity;
            worksheet.Cells[row, 8].Value = item.QuantityPurchase;
            worksheet.Cells[row, 9].Value = item.ItemsPosition;
            worksheet.Cells[row, 10].Value = item.TypePublish == TypePublish.Publish ? "TRUE" : "FALSE";
            worksheet.Cells[row, 11].Value = item.IsTop.ToString().ToUpper();
            worksheet.Cells[row, 12].Value = string.Join(";", item.Images?.Select(img => img.Link) ?? new List<string>());
        }
    }
    #endregion Import-Export-Excel ./
}

