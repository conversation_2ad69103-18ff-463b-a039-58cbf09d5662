using App.Base.Middleware;
using App.ECommerce.Setting;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Units;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class SyncServiceController : BaseController
{
    private readonly ISyncServiceFlow _syncServiceFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(SyncServiceController));
    private readonly IBaseFlow _baseFlow;

    public SyncServiceController(
        IStringLocalizer localizer,
        ISyncServiceFlow syncServiceFlow,
        IBaseFlow baseFlow) : base(localizer)
    {
        _syncServiceFlow = syncServiceFlow;
        _baseFlow = baseFlow;
    }

    [HttpPost]
    public async Task<IActionResult> CreateConfig([FromBody] SyncServiceConfigDto dto)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), dto.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            var saved = await _syncServiceFlow.SaveConfig(dto);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = saved });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in NhanhConfig: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpGet("{shopId}")]
    public async Task<IActionResult> GetConfig(string shopId, [FromQuery] SyncServiceEnum syncService)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            var config = await _syncServiceFlow.GetConfig(syncService, shopId);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = config });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetNhanhConfig: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateAccessCode([FromBody] UpdateAccessCodeDto dto)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), dto.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            var updated = await _syncServiceFlow.UpdateAccessCode(dto);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = updated });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in UpdateAccessCode: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpDelete("{shopId}")]
    public async Task<IActionResult> DeleteConfig(string shopId, [FromQuery] SyncServiceEnum syncService)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            var deleted = await _syncServiceFlow.DeleteConfig(syncService, shopId);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = deleted });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in DeleteNhanhConfig: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}