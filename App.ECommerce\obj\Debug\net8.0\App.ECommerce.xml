<?xml version="1.0"?>
<doc>
    <assembly>
        <name>App.ECommerce</name>
    </assembly>
    <members>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.GetPartners(App.ECommerce.Resource.Dtos.InputDtos.PartnerInputDto)">
            <summary>
            Get list partners (<PERSON><PERSON><PERSON> danh sách đối tác (Quản lý đối tác))
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list cart for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.GetPartner(System.String)">
            <summary>
            Get detail partner (<PERSON><PERSON><PERSON> thông tin chi tiết đối tác)
            </summary>
            <param name="id"></param>
            <returns>The result detail partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.GetReferers(System.String,System.String)">
            <summary>
            Get referers  (<PERSON><PERSON><PERSON> danh sách người giới thiệu của đối tác)
            </summary>
            <param name="ShopId"></param>
            <param name="UserId"></param>
            <returns>The list of referers</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.UpdatePartner(System.String,App.ECommerce.Resource.Dtos.InputDtos.UpdatePartnerDto)">
            <summary>
            Update partner details (Cập nhật thông tin đối tác)
            </summary>
            <param name="id">User ID</param>
            <param name="dto">Data update</param>
            <returns>Updated partner details</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.Approval(System.String)">
            <summary>
            Approval partner (Duyệt đối tác)
            </summary>
            <param name="id">User ID</param>
            <returns>Updated partner details</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.GetQuickReport(System.String)">
            <summary>
            Get quick report for a partner (Lấy báo cáo nhanh của đối tác)
            </summary>
            <param name="userId"></param>
            <returns>The quick report data</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationPartnerController.ExcelFile(App.ECommerce.Resource.Dtos.InputDtos.PartnerInputDto)">
            <summary>
            Export excel (Xuất excel danh sách đối tác (Quản lý đối tác))
            </summary>
            <param name="model"></param>
            <returns>Result excel file </returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.RegisterAffiliate(App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.RegisterAffiliateInputDto)">
            <summary>
            User đăng ký trở thành đối tác
            </summary>
            <param name="model"></param>
            <returns>Result list of user register Affiliate</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.AffiliateReport">
            <summary>
            Get affiliate report for a partner (Lấy báo cáo nhanh của đối tác)
            </summary>
            <returns>The quick report data</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyTeam(App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyTeamInputDto)">
            <summary>
            Get my team member
            </summary>
            <returns>Return MyTeam data</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyCommission(App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyCommissionInputDto)">
            <summary>
            Get my commission
            </summary>
            <returns>Return MyCommission data</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyCommissionReport(System.Int32,System.Int32)">
            <summary>
            Get list commission report
            </summary>
            <returns>Return MyCommission List Commission report</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.MyOrders(System.Int32,System.Int32)">
            <summary>
            Get list order
            </summary>
            <returns>Return ListMyOrders</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Affiliate.AffiliationUserController.IsCommissionActive(System.String)">
            <summary>
            check IsCommissionActive
            </summary>
            <returns>Return result of IsCommissionActive</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BaseController.IsLocal">
            <summary>
            Check host is local
            </summary>
            <returns></returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertisePartnerController.ListAdvertise(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list Advertise for Partner (Lấy danh sách popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list Advertise for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertisePartnerController.CreateAdvertise(App.ECommerce.Resource.Dtos.AdvertiseDto)">
            <summary>
            Create Advertise (Tạo mới popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateAdvertise</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertisePartnerController.UpdateAdvertise(App.ECommerce.Resource.Dtos.AdvertiseDto)">
            <summary>
            Update Advertise (Cập nhật thông tin popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result update Advertise</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertisePartnerController.DeleteAdvertise(System.String)">
            <summary>
            Delete Advertise (Xóa popup quảng cáo cho cửa hảng, chỉ hỗ trợ cho quản trị)
            </summary>
            <param name="advertiseId"></param>
            <returns>The result delete Advertise</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertisePartnerController.DetailAdvertise(System.String)">
            <summary>
            Get detail Advertise (Lấy thông tin chi tiết popup quảng cáo cho cửa hảng)
            </summary>
            <param name="advertiseId"></param>
            <returns>The result detail Advertise</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.ListArticleCategory(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list ArticleCategory for shop (Danh sách danh mục bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.DetailArticleCategory(System.String)">
            <summary>
            Get detail ArticleCategory for shop (Chi tiết danh mục bài viết cho cửa hàng)
            </summary>
            <param name="articleCategoryId"></param>
            <returns>Result Detail ArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.CreateArticleCategory(App.ECommerce.Resource.Dtos.ArticleCategoryDto)">
            <summary>
            Create ArticleCategory for shop (Tạo mới danh mục bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.UpdateIconArticleCategory(App.ECommerce.Controllers.API.ArticlePartnerController.UpdateIconArticleCategoryModel)">
            <summary>
            Update icon ArticleCategory for shop (Cập nhật icon danh mục bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateIconArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.UpdateArticleCategory(App.ECommerce.Resource.Dtos.ArticleCategoryDto)">
            <summary>
            Update ArticleCategory for shop (Cập nhật danh mục bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.DeleteArticleCategory(System.String)">
            <summary>
            Delete ArticleCategory for shop (Xoá danh mục bài viết cho cửa hàng)
            </summary>
            <param name="articleCategoryId"></param>
            <returns>The result DeleteArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.ListArticle(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list Article for shop (Danh sách bài viết của cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.CreateArticle(App.ECommerce.Resource.Dtos.ArticleDto)">
            <summary>
            Create Article (Tạo mới bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.UpdateArticle(App.ECommerce.Resource.Dtos.ArticleDto)">
            <summary>
            Update Article (Cập nhật thông tin bài viết cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.DeleteArticle(System.String)">
            <summary>
            Delete Article (Xóa bài viết cho cửa hảng)
            </summary>
            <param name="articleId"></param>
            <returns>The result DeleteArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticlePartnerController.DetailArticle(System.String)">
            <summary>
            Get detail Article (Lấy thông tin chi tiết bài viết cho cửa hảng)
            </summary>
            <param name="articleId"></param>
            <returns>The result DetailArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthPartnerController.Login(App.ECommerce.Controllers.API.AuthPartnerController.LoginPartnerModel)">
            <summary>
            Login for Partner, require password encode md5
            </summary>
            <param name="model"></param>
            <returns>Result login for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthPartnerController.RefreshToken(System.String)">
            <summary>
            Refresh token for Partner logged
            </summary>
            <param name="refreshToken"></param>
            <returns>New token for Partner logged</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthPartnerController.Logout(System.String,System.String)">
            <summary>
            Logout for Partner logged
            </summary>
            <param name="refreshToken"></param>
            <param name="tokenFCM"></param>
            <returns>Result logout for Partner logged</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Balance_LogController.GetListByPartnerId(App.ECommerce.Resource.Dtos.InputDtos.BalanceLogFilterDto)">
            <summary>
            Get list of blance log by partnerId (Lấy danh sách log số dư theo partnerId)
            </summary>
            <returns>Danh sách log blance</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.ListBranch(System.String,System.Int32,System.Int32)">
            <summary>
            Get list Branch for Partner (Lấy danh sách popup quảng cáo cho cửa hảng)
            </summary>
            <param name="shopId"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list Branch for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.CreateBranch(App.ECommerce.Resource.Dtos.BranchDto)">
            <summary>
            Create Branch (Tạo mới popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateBranch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.UpdateImageBranch(App.ECommerce.Controllers.API.BranchController.UpdateImageBranchModel)">
            <summary>
            Update image for Branch (Cập nhật hình ảnh popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result upload image Branch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.UpdateBranch(App.ECommerce.Resource.Dtos.BranchDto)">
            <summary>
            Update Branch (Cập nhật thông tin popup quảng cáo cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result update Branch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.DeleteBranch(System.String)">
            <summary>
            Delete Branch (Xóa popup quảng cáo cho cửa hảng, chỉ hỗ trợ cho quản trị)
            </summary>
            <param name="branchId"></param>
            <returns>The result delete Branch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchController.DetailBranch(System.String)">
            <summary>
            Get detail Branch (Lấy thông tin chi tiết popup quảng cáo cho cửa hảng)
            </summary>
            <param name="branchId"></param>
            <returns>The result detail Branch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.GetListCampaigns(App.ECommerce.Resource.Dtos.InputDtos.CampaignFilterDto)">
            <summary>
            Get list campaigns (Lấy danh sách campaign)
            </summary>
            <param name="obj">Thông tin tìm kiếm</param>
            <returns>Result list campaigns</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.GetInfoCampaignById(System.String,System.String)">
            <summary>
            Get campaign detail (Lấy thông tin chi tiết campaign)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="campaignId">ID của chiến dịch</param>
            <returns>Result info campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.CreateCampaign(App.ECommerce.Resource.Dtos.InputDtos.CampaignInputDto)">
            <summary>
            Create new campaign (Tạo mới campaign)
            </summary>
            <param name="obj">Thông tin chi tiết chiến dịch</param>
            <returns>Result info campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.UpdateCampaign(App.ECommerce.Resource.Dtos.InputDtos.CampaignInputDto)">
            <summary>
            Update campaign (Cập nhật thông tin campaign)
            </summary>
            <param name="obj">Thông tin chi tiết chiến dịch</param>
            <returns>Result update campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.DeleteCampaign(System.String,System.String)">
            <summary>
            Delete campaign (Xóa campaign)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="campaignId">ID của chiến dịch</param>
            <returns>Delete campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.GetListUserByCampaign(App.ECommerce.Resource.Dtos.InputDtos.CampaignUserFilterDto)">
            <summary>
            Get list users of campaign (Lấy danh sách users của campaign)
            </summary>
            <param name="obj">Tham số tìm kiếm</param>
            <returns>List users of campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.CreateCampaignUser(App.ECommerce.Resource.Dtos.InputDtos.CampaignUserDto)">
            <summary>
            Create new campaign user (Tạo mới campaign user)
            </summary>
            <param name="obj">Thông tin chi tiết gửi chiến dịch</param>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.ImportTemplateZNS(Microsoft.AspNetCore.Http.IFormFile,App.ECommerce.Resource.Dtos.InputDtos.ImportUserZNSCampaignInputDto)">
            <summary>
            Import users campaign from Xlsx file (Nhập dữ liệu người dùng từ file Xlsx)
            </summary>
            <param name="file">Xlsx file containing user data</param>
            <param name="obj"></param>
            <returns>Result of the import process</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CampaignController.ChangeCampaignStatus(System.String,System.String)">
            <summary>
            Change campaign status between Running and Paused (Thay đổi trạng thái campaign giữa Running và Paused)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="campaignId">ID của chiến dịch</param>
            <returns>Result change status campaign</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.SearchUser(App.ECommerce.Controllers.API.CartPartnerController.SearchUserDto,System.Int32,System.Int32)">
            <summary>
            Get list User of shop for Partner (Lấy danh sách người dùng theo tên)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListUser for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.SearchItems(App.ECommerce.Controllers.API.CartPartnerController.SearchItemsDto,System.Int32,System.Int32)">
            <summary>
            Search items for shop (Tìm kiếm sản phẩm / dịch vụ theo biến thể nếu có)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result SearchItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.ListCart(App.ECommerce.Resource.Dtos.SearchCartDto,System.Int32,System.Int32)">
            <summary>
            Get list cart for Partner (Lấy danh sách đơn hàng cho cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list cart for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.GetCart(System.String)">
            <summary>
            Get detail cart for shop (Lấy thông tin chi tiết đơn hàng cho cửa hàng)
            </summary>
            <param name="cartId"></param>
            <returns>The result GetCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.CreateOrUpdateCart(App.ECommerce.Resource.Dtos.CartDto)">
            <summary>
            Create or update cart (Tạo mới hoặc cập nhật giỏ hàng cho cửa hàng, tạo mới transactionId is empty)
            </summary>
            <param name="model"></param>
            <returns>The result CreateOrUpdateCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.DeleteCart(System.String)">
            <summary>
            Delete cart
            </summary>
            <param name="cartId"></param>
            <returns>The result DeleteCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartPartnerController.EstimatePointCart(System.String)">
            <summary>
            Kiểm tra điểm khả dụng mua hàng của user
            </summary>
            <param name="cartId"></param>
            <returns>The result GetMaxExchangePointByCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.TreeCategory(App.ECommerce.Resource.Dtos.InputDtos.CategoryFilterDto)">
            <summary>
            Get tree Category for shop (Danh sách danh mục dạng cây theo loại cho cửa hàng)
            </summary>
            <param name="filter"></param>
            <returns>Result TreeCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.ListCategory(App.ECommerce.Resource.Dtos.InputDtos.CategoryFilterDto)">
            <summary>
            Get list Category for shop (Danh sách danh mục theo loại cho cửa hàng)
            </summary>
            <param name="filter"></param>
            <returns>Result ListCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.CreateCategory(App.ECommerce.Resource.Dtos.CategoryDto)">
            <summary>
            Create Category for shop (Tạo mới danh mục cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.UpdateCategory(App.ECommerce.Resource.Dtos.CategoryDto)">
            <summary>
            Update Category for shop (Cập nhật danh mục cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.DeleteCategory(System.String)">
            <summary>
            Delete Category for shop (Xoá danh mục cho cửa hàng)
            </summary>
            <param name="categoryId"></param>
            <returns>The result DeleteCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.DetailCategory(System.String)">
            <summary>
            Get detail Category (Lấy thông tin chi tiết bài viết cho cửa hảng)
            </summary>
            <param name="categoryId"></param>
            <returns>The result DetailCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.ExportTemplateCategory(System.String,App.Base.Repository.Entities.TypeCategory)">
            <summary>
            Export Excel template for category import (Xuất file Excel mẫu để nhập dữ liệu danh mục)
            </summary>
            <returns>CSV file with template for user import</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.ImportCategory(Microsoft.AspNetCore.Http.IFormFile,System.String,App.Base.Repository.Entities.TypeCategory)">
            <summary>
            Import users from XLSX file (Nhập dữ liệu người dùng từ file XLSX)
            </summary>
            <param name="file">XLSX file containing user data</param>
            <param name="shopId">ID of the shop</param>
            <param name="categoryType">Type Category</param>
            <returns>Result of the import process</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryPartnerController.ExportListCategory(App.ECommerce.Resource.Dtos.InputDtos.CategoryFilterDto)">
            <summary>
            Export Excel Category for shop (Xuất Excel danh sách danh mục theo loại cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>Result ListCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DashboardPartnerController.DashboardInfo(System.String)">
            <summary>
            Get list Dashboard for Partner 
            </summary>
            <param name="shopId"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result dashboard for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DashboardPartnerController.DashboardInfoV2(App.ECommerce.Resource.Dtos.ParamDashboard)">
            <summary>
            Get list Dashboard for Partner 
            </summary>
            <param name=""></param>
            <param name="data"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result dashboard for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DomainNamePartnerController.CreateDomainName(App.ECommerce.Resource.Dtos.DomainNameDto)">
            <summary>
             Partner create domain name for shop (Đối tác tạo tên miền cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>Result result domain name for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DomainNamePartnerController.CheckDomainNameExistAsync(System.String)">
            <summary>
             Partner check if domain name exist (Đối tác kiểm tra tên miền đã tồn tại chưa)
            </summary>
            <param name="domain"></param>
            <returns>Result of CheckDomainNameExist</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DomainNamePartnerController.UpdateDomainName(App.ECommerce.Resource.Dtos.DomainNameDto)">
            <summary>
             Partner update domain name  (Đối tác cập nhật tên miền)
            </summary>
            <param name="model"></param>
            <returns>Result of UpdateDomainName</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DomainNamePartnerController.ListShopDomain(System.String)">
            <summary>
             List domain name of shop (Danh sách tên miền của cửa hàng)
            </summary>
            <param name="shopId"></param>
            <returns>Result of ListShopDomain</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.DomainNamePartnerController.DeleteDomainNames(System.Collections.Generic.List{System.String})">
            <summary>
            Delete domain names (Xóa nhiều tên miền của 1 shop)
            </summary>
            <param name="domainNameIds"></param>
            <returns>Result of DeleteDomainNames</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.ListGroup(App.ECommerce.Resource.Dtos.InputDtos.FileGroupFilterDto)">
            <summary>
            Get list groups
            </summary>
            <param name="model"></param>
            <returns>Result list GroupFile for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.CreateGroup(App.ECommerce.Resource.Dtos.GroupFileDto)">
            <summary>
            Create group
            </summary>
            <param name="model"></param>
            <returns>The result CreateGroup</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.UpdateGroup(App.ECommerce.Resource.Dtos.GroupFileDto)">
            <summary>
            Update group
            </summary>
            <param name="model"></param>
            <returns>The result update GroupFile</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.DetailGroup(System.String,System.String)">
            <summary>
            Update group
            </summary>
            <param name="shopId"></param>
            <param name="groupFileId"></param>
            <returns>The result update GroupFile</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.DeleteGroup(System.String)">
            <summary>
            Delete group
            </summary>
            <param name="groupFileId"></param>
            <returns>The result delete GroupFile</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.ListFile(App.ECommerce.Resource.Dtos.InputDtos.FileFilterDto)">
            <summary>
            Get list file from group
            </summary>
            <param name="model"></param>
            <returns>Result list MediaFile for group</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.CreateFile(App.ECommerce.Resource.Dtos.InputDtos.FileDto)">
            <summary>
            Create file
            </summary>
            <param name="model"></param>
            <returns>The result CreateFile</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.FileManageController.DeleteFile(System.String)">
            <summary>
            Delete file
            </summary>
            <param name="mediaFileId"></param>
            <returns>The result delete MediaFile</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceConfigController.GetInvoiceConfig(System.String,App.ECommerce.Units.Enums.Invoice.InvoiceProviderEnum)">
            <summary>
            Lấy danh sách cấu hình hóa đơn điện tử của cửa hàng theo nhà cung cấp
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceConfigController.CreateInvoiceConfig(App.ECommerce.Resource.Dtos.InvoiceConfigRequestDto)">
            <summary>
            Tạo mới cấu hình hóa đơn cho đối tác.
            </summary>
            <param name="obj">Thông tin cấu hình hóa đơn.</param>
            <returns>Trả về ID của cấu hình hóa đơn vừa tạo hoặc thông báo lỗi nếu thất bại.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceConfigController.GetInvoiceConfig(System.String)">
            <summary>
            Lấy thông tin chi tiết của một cấu hình hóa đơn theo ID.
            </summary>
            <param name="id">ID của cấu hình hóa đơn.</param>
            <returns>Trả về thông tin cấu hình hóa đơn hoặc thông báo lỗi nếu không tìm thấy.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceConfigController.UpdateInvoiceConfig(System.String,App.ECommerce.Resource.Dtos.InvoiceConfigRequestDto)">
            <summary>
            Cập nhật cấu hình hóa đơn theo ID.
            </summary>
            <param name="id">ID của cấu hình hóa đơn cần cập nhật.</param>
            <param name="request">Thông tin cấu hình hóa đơn mới.</param>
            <returns>Trả về ID của cấu hình hóa đơn đã cập nhật hoặc thông báo lỗi nếu thất bại.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceConfigController.ActiveInvoiceConfig(System.String)">
            <summary>
            Kích hoạt cấu hình hóa đơn theo ID.
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceController.GetListInvoiceHistory(App.ECommerce.Resource.Dtos.InputDtos.InvoiceFilterInputDto)">
            <summary>
            Get list history invoice (Lấy danh sách lịch sử xuất hoá đơn)
            </summary>
            <param name="obj">Điều kiện tìm kiếm</param>
            <returns>Result list invoice history</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceController.CreateManualInvoice(System.String)">
            <summary>
            Tạo hóa đơn thủ công cho một đơn hàng.
            </summary>
            <param name="orderId">ID đơn hàng cần xuất hoá đơn</param>
            <returns>Trả về thông tin hóa đơn được tạo hoặc thông báo lỗi nếu thất bại.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.InvoiceController.DownloadInvoicePdf(System.String)">
            <summary>
            Download file pdf Invoice (Tải file PDF của hóa đơn đã xuất)
            </summary>
            <param name="invoiceNo">Số hóa đơn cần tải.</param>
            <returns>Trả về file PDF của hóa đơn hoặc thông báo lỗi nếu thất bại.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.GetAllItemOptions(App.ECommerce.Resource.Dtos.InputDtos.ItemOptionFilterDto)">
            <summary>
            Get all item options
            </summary>
            <param name="obj"></param>
            <returns>List item options</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.GetItemOptionsByGroup(System.String,System.Int32,System.Int32)">
            <summary>
            Get all item options by group
            </summary>
            <param name="itemOptionGroupId">id of item option group</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to return</param>
            <returns>List item options by group</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.GetById(System.String)">
            <summary>
            Get one item option by id
            </summary>
            <param name="id">id of item option</param>
            <returns>Item option detail</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.Upsert(System.Collections.Generic.List{App.ECommerce.Repository.Entities.ItemOption},System.String)">
            <summary>
            Create or Update item option
            </summary>
            <param name="model">Item option data</param>
            <returns>Created or updated item option</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.Delete(System.String)">
            <summary>
            Delete item option
            </summary>
            <param name="id">Item option id</param>
            <returns>Success status</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionController.ValidateItemOption(App.ECommerce.Repository.Entities.ItemOption)">
            <summary>
            validate name and price of item option
            </summary>
            <param name="model">Item option</param>
            <returns>custom bad request model or null </returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionGroupController.GetList(System.String,System.Int32,System.Int32)">
            <summary>
            Get list of item option groups for shop
            </summary>
            <param name="shopId">Shop ID</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to return</param>
            <returns>List of item option groups</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionGroupController.GetById(System.String)">
            <summary>
            Get item option group by ID
            </summary>
            <param name="id">Item option group ID</param>
            <returns>Item option group details</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionGroupController.Create(App.ECommerce.Repository.Entities.ItemOptionGroup)">
            <summary>
            Create new item option group
            </summary>
            <param name="model">Item option group data</param>
            <returns>Created item option group</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionGroupController.Update(System.String,App.ECommerce.Repository.Entities.ItemOptionGroup)">
            <summary>
            Update existing item option group
            </summary>
            <param name="id">Item option group ID</param>
            <param name="model">Updated item option group data</param>
            <returns>Updated item option group</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionGroupController.Delete(System.String)">
            <summary>
            Delete item option group
            </summary>
            <param name="id">Item option group ID to delete</param>
            <returns>Success status</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.CreateProduct(App.ECommerce.Resource.Dtos.ProductDto)">
            <summary>
            Create product for shop (Tạo mới sản phẩm cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateProduct for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.UpdateProduct(App.ECommerce.Resource.Dtos.ProductDto)">
            <summary>
            Update product for shop (Cập nhật sản phẩm cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateProduct for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.DeleteProduct(System.String)">
            <summary>
            Delete product for shop (Xoá sản phẩm cho cửa hàng, xoá tấc cả biến thể đi kèm)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DeleteProduct for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.CreateService(App.ECommerce.Resource.Dtos.ServiceDto)">
            <summary>
            Create service for shop (Tạo mới dịch vụ cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateService for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.UpdateService(App.ECommerce.Resource.Dtos.ServiceDto)">
            <summary>
            Update service for shop (Cập nhật dịch vụ cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateService for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.DeleteService(System.String)">
            <summary>
            Delete service for shop (Xoá dịch vụ cho cửa hàng)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DeleteService for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ListItems(App.ECommerce.Resource.Dtos.InputDtos.ItemFilterDto)">
            <summary>
            Get list items for shop (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>Result ListItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.DetailGroupItems(System.String)">
            <summary>
            Get detail items group variant for shop (Lấy thông tin chi tiết sản phẩm hoặc dịch vụ nhóm theo biến thể)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DetailGroupItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.DetailItems(System.String)">
            <summary>
            Get detail items for shop (Lấy thông tin chi sản phẩm hoặc dịch vụ, không phân nhóm theo biến thể)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DetailItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ListItemsByItemsIdAsync(App.ECommerce.Controllers.API.ItemsPartnerController.ListItemsByItemsIdDto)">
            <summary>
            Get list items by itemsIds (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng theo 1 list itemsId)
            </summary>
            <param name="model"></param>
            <returns>Result ListItemsByItemsId for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ListItemsByItemsCode(App.ECommerce.Controllers.API.ItemsPartnerController.ListItemsByItemsCodeDto)">
            <summary>
            Get list items by itemsCodes (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng theo 1 list itemsCode)
            </summary>
            <param name="model"></param>
            <returns>Result ListItemsByItemsCode for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ExportProductTemplate(System.String,App.ECommerce.Repository.Entities.TypeItems)">
            <summary>
            Xuất file Excel mẫu để nhập sản phẩm
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="type">Loại sản phẩm</param>
            <returns>File Excel mẫu</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ImportItems(Microsoft.AspNetCore.Http.IFormFile,System.String,App.ECommerce.Repository.Entities.TypeItems)">
            <summary>
            Nhập sản phẩm hoặc dịch vụ từ file Excel
            </summary>
            <param name="file">File Excel chứa danh sách sản phẩm hoặc dịch vụ</param>
            <param name="shopId">ID của cửa hàng</param>
            <param name="type">Loại mục (Product/Service)</param>
            <returns>Kết quả nhập liệu</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ExportItems(App.ECommerce.Resource.Dtos.InputDtos.ItemPartnerFilterDto)">
            <summary>
            Xuất danh sách sản phẩm hoặc dịch vụ dưới dạng file Excel dựa trên bộ lọc
            </summary>
            <param name="model">Bộ lọc để lấy danh sách sản phẩm hoặc dịch vụ</param>
            <returns>File Excel chứa danh sách sản phẩm hoặc dịch vụ</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.ValidatePackageItemsLimit(App.ECommerce.Repository.Entities.Partner,System.String,App.ECommerce.Repository.Entities.TypeItems,System.Int32)">
            <summary>
            Kiểm tra giới hạn số lượng sản phẩm/dịch vụ theo gói cho trường hợp import
            </summary>
            <param name="partner">Thông tin partner</param>
            <param name="shopId">ID của shop</param>
            <param name="itemsType">Loại items (Product/Service)</param>
            <param name="count">Số lượng items mới</param>
            <returns>CustomBadRequest nếu vượt quá giới hạn, null nếu hợp lệ</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.DeleteItems(System.String,App.ECommerce.Repository.Entities.TypeItems,System.Collections.Generic.List{System.String})">
            <summary>
            Delete item for shop (Xoá item cho cửa hàng, xoá tấc cả biến thể đi kèm)
            </summary>
            <param name="shopId"></param>
            <param name="itemsType"></param>
            <param name="itemCodes"></param>
            <returns>The result DeleteProduct for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsPartnerController.CleanHtmlContent(System.String)">
            <summary>
            Làm sạch HTML content từ text editor, loại bỏ các khoảng trắng không cần thiết
            </summary>
            <param name="htmlContent">Nội dung HTML từ text editor</param>
            <returns>Nội dung đã được làm sạch</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.GetAllJobs">
            <summary>
            Get all jobs in system (Lấy danh sách tất cả các jobs trong hệ thống)
            </summary>
            <returns>List of jobs information (Danh sách thông tin các jobs)</returns>
            GET: api/user/jobmanager
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.GetJobById(System.Guid)">
            <summary>
            Get job details by id (Lấy thông tin chi tiết của một job theo id)
            </summary>
            <param name="id">Job id (Id của job cần lấy thông tin)</param>
            <returns>Job details information (Thông tin chi tiết của job)</returns>
            GET: api/user/jobmanager/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.CreateJob(App.ECommerce.Resource.Dtos.QuartzJobRequestDto)">
            <summary>
            Create new job in system (Tạo mới một job trong hệ thống)
            </summary>
            <param name="objRequest">Object contains job information (Object chứa thông tin job cần tạo)</param>
            <returns>Id of created job (Id của job vừa được tạo)</returns>
            POST: api/user/jobmanager/create
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.UpdateJob(App.ECommerce.Resource.Dtos.QuartzJobRequestDto)">
            <summary>
            Update job information (Cập nhật thông tin của một job)
            </summary>
            <param name="objRequest">Object contains job information to update (Object chứa thông tin job cần cập nhật)</param>
            <returns>Id of updated job (Id của job đã cập nhật)</returns>
            PUT: api/user/jobmanager/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.DeleteJob(System.Guid)">
            <summary>
            Delete a job from system (Xóa một job khỏi hệ thống)
            </summary>
            <param name="id">Job id to delete (Id của job cần xóa)</param>
            <returns>Id of deleted job (Id của job đã xóa)</returns>
            DELETE: api/user/jobmanager/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.PauseJob(System.Guid)">
            <summary>
            Pause job execution (Tạm dừng thực thi một job)
            </summary>
            <param name="id">Job id to pause (Id của job cần tạm dừng)</param>
            <returns>Id of paused job (Id của job đã tạm dừng)</returns>
            POST: api/user/jobmanager/{id}/pause
        </member>
        <member name="M:App.ECommerce.Controllers.API.JobManagerController.ResumeJob(System.Guid)">
            <summary>
            Resume execution of a paused job (Tiếp tục thực thi một job đã tạm dừng)
            </summary>
            <param name="id">Job id to resume (Id của job cần tiếp tục thực thi)</param>
            <returns>Execution result (Kết quả thực thi)</returns>
            POST: api/user/jobmanager/{id}/resume
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.GetListMembershipLevel(System.String,System.Int32,System.Int32)">
            <summary>
            Get detail MembershipLevel. (Lấy thông tin điểm xếp hạng điểm thành viên)
            </summary>
            <param name="shopId"></param>
            <returns>Get member rating score information</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.CreateMembershipLevel(App.ECommerce.Resource.Dtos.MembershipCreateDto)">
            <summary>
            Create MembershipLevel (Tạo mới Danh sách xếp hạng tích điểm)
            </summary>
            <param name="model"></param>
            <returns>The result MembershipLevel</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.UpdateMembershipLevel(App.ECommerce.Resource.Dtos.MembershipEditDto)">
            <summary>
            Update Shop (Cập nhật thông tin danh sách xếp hạng tích điểm)
            </summary>
            <returns>The result update membership level</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.DeleteMembershipLevel(System.String)">
            <summary>
            Delete MembershipLevel (Xóa hạng của mục tích điểm xếp hạng rank)
            </summary>
            <param name="levelId"></param>
            <returns>The result delete MembershipLevel</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.SummaryPoints(System.String)">
            <summary>
            Lấy thông tin tổng điểm tích điểm và tổng người dùng tích điểm.
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <returns>Thông tin tổng điểm</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.UserPointsHistory(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get ListDetailUserScore. (Lấy thông tin danh sách chi điểm người dùng)
            </summary>
            <param name = "shopId" > Shop ID</param>
            <param name = "search" > Search keyword</param>
            <param name = "skip" > Number of records to skip</param>
            <param name = "limit" > Number of records to take</param>
            <returns>Get List Detail User Score</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.ModifyUserPoints(App.ECommerce.Resource.Dtos.UpdateUserPointsDto)">
            <summary>
            Cập nhật điểm của người dùng.
            </summary>
            <param name="model">Dữ liệu cập nhật điểm.</param>
            <returns>Kết quả cập nhật điểm.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.PointExchangeHistory(System.String,System.String,System.Int32,System.Int32,App.ECommerce.Repository.Entities.TypeTransaction,System.String,System.String)">
            <summary>
            Get ListPointExchangeHistory. (Lấy thông tin danh sách Lịch sử đổi điểm)
            </summary>
            <param name="shopId">Shop ID</param>
            <param name="search">Search keyword</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to take</param>
            <returns>Get List Point Exchange History</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.IncomeHistory(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get GetIncomeHistory. (Lấy thông tin lịch sử cộng điểm)
            </summary>
            <param name="shopId">Number of records to skip</param>
            <param name="userId">Number of records to skip</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to take</param>
            <returns>Get Income History</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.SpendingHistory(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get GetSpendingHistory. (Lấy thông tin lịch sử trừ điểm)
            </summary>
            <param name="shopId">Number of records to skip</param>
            <param name="userId">Number of records to skip</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to take</param>
            <returns>Get Spending History</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.MembershipConfig(System.String)">
            <summary>
            GetConfigMembership. (Lấy thông tin config thiệt lập quy tắc điểm)
            </summary>
            <param name="shopId"></param>
            <returns>Get data Config Membership</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.ModifyMembershipConfig(App.ECommerce.Resource.Dtos.ConfigMembershipLevelDto)">
            <summary>
            EditConfigMembership. (Sửa config thiệt lập quy tắc điểm)
            </summary>
            <param name="model"></param>
            <returns>Edit Config Membership</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelController.UpdateImageMembershipLevel(App.ECommerce.Controllers.API.MembershipLevelController.UpdateImageMembershipLevelModel)">
            <summary>
            Update image membershiplevel
            </summary>
            <param name="model"></param>
            <returns>The result UpdateImageMembershipLevel for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.CreateOrder(App.ECommerce.Resource.Dtos.CartDto)">
            <summary>
            Create order (Tạo mới đơn hàng, tạo mới transactionId is empty)
            </summary>
            <param name="obj"></param>
            <returns>The result CreateOrder for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.ListOrder(App.ECommerce.Resource.Dtos.InputDtos.ListOrderInputDto,System.Int32,System.Int32)">
            <summary>
            Get list order for Partner (Lấy danh sách đơn hàng cho cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list order for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.OrderDetail(System.String,System.String)">
            <summary>
            Get Order Detail (Lấy thông tin chi tiết đơn hàng)
            </summary>
            <param name="orderId"></param>
            <param name="shopId"></param>
            <returns>The result OrderDetail</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.UpdateOrderInfo(App.ECommerce.Resource.Dtos.InputDtos.UpdateOrderInfoDto)">
            <summary>
            Partner update order info (Đối tác cập nhật thông tin đơn hàng)
            </summary>
            <param name="model"></param>
            <returns>The result Update</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.ListOrderByUserId(App.ECommerce.Resource.Dtos.InputDtos.ListOrderByUserIdInputDto,System.Int32,System.Int32)">
            <summary>
            Get list order of an user for Partner (Lấy danh sách đơn hàng của 1 khách hàng cho đối tác)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list order for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.PrintTransportOrders(App.ECommerce.Resource.Dtos.PrintTransportOrdersDto)">
            <summary>
            Partner PrintTransportOrder (Đối tác in hóa đơn đơn hàng)
            </summary>
            <param name="model">ID đơn hàng cần in hóa đơn</param>
            <returns>Result of printing (file PDF merged)</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.TraceTransportOrder(System.String)">
            <summary>
            Partner TraceTransportOrder (Đối tác ta cứu đơn đơn hàng)
            </summary>
            <param name="orderId">ID đơn hàng cần tra cứu</param>
            <returns>Result of TraceTransportOrder</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderPartnerController.GetTotalOrderStatus(App.ECommerce.Resource.Dtos.InputDtos.ListOrderInputDto)">
            <summary>
            Toltal Order Status (Tổng số lượng đơn hàng theo trạng thái)
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OtpPartnerController.SendOTP(App.ECommerce.Controllers.API.OtpPartnerController.SendOtpPartnerModel)">
            <summary>
            Send OTP (SDT tối đa 20 lần/ngày, IP tối đa 10 lần/ngày, OTP hết hạn trong 5 phút)
            </summary>
            <returns>The result send OTP</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OtpPartnerController.VerifyOTP(App.ECommerce.Controllers.API.OtpPartnerController.VerifyOtpPartnerModel)">
            <summary>
            Verify OTP (SDT tối đa 20 lần/ngày, IP tối đa 10 lần/ngày, OTP hết hạn trong 5 phút)
            </summary>
            <param name="model"></param>
            <returns>The result verify OTP</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.CreateEmployee(CreateEmployeeDto)">
            <summary>
            Tạo mới một nhân viên cho đối tác với thông tin cá nhân và vai trò được gán
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.GetEmployees(App.ECommerce.Resource.Model.Paging,System.String)">
            <summary>
            Lấy danh sách nhân viên của đối tác, hỗ trợ phân trang và lọc theo vai trò
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.GetEmployee(System.String)">
            <summary>
            Lấy thông tin chi tiết của một nhân viên theo ID
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.UpdateEmployee(System.String,UpdateEmployeeDto)">
            <summary>
            Cập nhật thông tin và vai trò của một nhân viên hiện có
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.DeleteMultipleEmployees(System.Collections.Generic.List{System.String})">
            <summary>
            Xóa nhiều nhân viên cùng lúc
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerEmployeeController.UpdateEmployeePassword(System.String,UpdateEmployeeDto)">
            <summary>
            Cập nhật mật khẩu cho một nhân viên theo ID
            </summary>
            <param name="employeeId">ID của nhân viên</param>
            <param name="dto">Dữ liệu chứa mật khẩu mới</param>
            <returns>Thông tin kết quả cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerFunctionController.PurchasePackage(App.ECommerce.Resource.Dtos.PurchasePackageDto)">
            <summary>
            Mua gói chức năng
            </summary>
            <param name="dto">Thông tin gói chức năng cần mua (PackageId, PaymentMethod, InvoiceNumber)</param>
            <returns>Trả về thông tin lịch sử mua gói nếu thành công, hoặc mã lỗi nếu thất bại</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerFunctionController.GetActivePackageFunctions">
            <summary>
            Lấy danh sách chức năng của gói đang hoạt động
            </summary>
            <returns>Trả về gói đang hoạt động cùng các chức năng liên quan, hoặc danh sách rỗng nếu không có gói nào</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerFunctionController.GetPackageHistory">
            <summary>
            Lấy lịch sử mua gói của đối tác
            </summary>
            <returns>Trả về danh sách lịch sử mua gói của đối tác</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerFunctionController.GetAvailablePackages">
            <summary>
            Lấy danh sách các gói chức năng có sẵn để xem và đăng ký
            </summary>
            <returns>Trả về danh sách các gói chức năng có sẵn cùng chi tiết chức năng, hoặc danh sách rỗng nếu không có gói nào</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:App.ECommerce.Controllers.API.PartnerFunctionController.CheckPermission(App.ECommerce.Resource.Dtos.CheckPermissionDto)" -->
        <member name="M:App.ECommerce.Controllers.API.PartnerFunctionController.GetAllPermissions">
            <summary>
            Lấy tất cả quyền truy cập cho đối tác hoặc nhân viên
            </summary>
            <returns>Trả về danh sách tất cả URL và quyền liên quan, cùng trạng thái isAgency</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerRoleController.CreateRole(CreateRoleDto)">
            <summary>
            Tạo mới một vai trò cho đối tác với các chức năng và quyền tương ứng
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerRoleController.GetRoles">
            <summary>
            Lấy danh sách tất cả vai trò
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerRoleController.GetRole(System.String)">
            <summary>
            Lấy thông tin chi tiết của một vai trò cụ thể theo ID
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerRoleController.UpdateRole(System.String,UpdateRoleDto)">
            <summary>
            Cập nhật thông tin và chức năng của một vai trò hiện có
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PartnerRoleController.DeleteRole(System.String)">
            <summary>
            Xóa hoàn toàn một vai trò và các chức năng liên quan
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.ListPayment(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list Payment for shop (Danh sách phương thức thanh toán của cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListPayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.CreatePayment(App.ECommerce.Resource.Dtos.PaymentDto)">
            <summary>
            Create Payment (Tạo mới phương thức thanh toán cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreatePayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.UpdatePhotoPayment(App.ECommerce.Controllers.API.PaymentPartnerController.UpdatePhotoPaymentModel)">
            <summary>
            Update photo for payment (Cập nhật ảnh cho phương thức thanh toán)
            </summary>
            <param name="model"></param>
            <returns>The result upload photo for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.UpdatePayment(App.ECommerce.Resource.Dtos.PaymentDto)">
            <summary>
            Update Payment (Cập nhật phương thức thanh toán cho cửa hảng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdatePayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.DeletePayment(System.String)">
            <summary>
            Delete Payment (Xóa phương thức thanh toán cho cửa hàng)
            </summary>
            <param name="paymentId"></param>
            <returns>The result DeletePayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentPartnerController.DetailPayment(System.String)">
            <summary>
            Get detail Payment (Lấy thông tin phương thức thanh toán cho cửa hảng)
            </summary>
            <param name="paymentId"></param>
            <returns>The result DetailPayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetListPriceLists(App.ECommerce.Resource.Dtos.InputDtos.PriceListFilterDto)">
            <summary>
            Get list pricelists (Lấy danh sách bảng giá)
            </summary>
            <param name="obj">Thông tin tìm kiếm</param>
            <returns>Result list price lists</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetInfoPriceListById(System.String,System.String)">
            <summary>
            Get price list detail (Lấy thông tin chi tiết bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <returns>Result info price list</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.CreatePriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListInputDto)">
            <summary>
            Create new price list (Tạo mới bảng giá)
            </summary>
            <param name="obj">Thông tin chi tiết bảng giá</param>
            <returns>Result info price list</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.UpdatePriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListInputDto)">
            <summary>
            Update price list (Cập nhật thông tin bảng giá)
            </summary>
            <param name="obj">Thông tin chi tiết bảng giá</param>
            <returns>Result update price list</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.DeletePriceList(System.String,System.String)">
            <summary>
            Delete price list (Xóa bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <returns>Result delete price list</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.DeleteManyPriceLists(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Delete multiple price lists (Xóa nhiều bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListIds">Danh sách ID của các bảng giá cần xóa</param>
            <returns>Result delete multiple price lists</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.AddPriceListItems(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Add items for price list (Thêm sản phẩm/dịch vụ cho bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="items">Danh sách sản phẩm/dịch vụ</param>
            <returns>Result add items</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.RemoveItemsAsync(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Add items for price list (Thêm sản phẩm/dịch vụ cho bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="items">Danh sách sản phẩm/dịch vụ</param>
            <returns>Result add items</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.UpdatePriceListBranches(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Update branches for price list (Cập nhật chi nhánh cho bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="branchIds">Danh sách ID chi nhánh</param>
            <returns>Result update branches</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.UpdatePriceListRanks(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Update ranks for price list (Cập nhật hạng khách hàng cho bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="rankIds">Danh sách ID hạng khách hàng</param>
            <returns>Result update ranks</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.UpdateItemQuantityTiers(System.String,System.String,System.Collections.Generic.List{App.ECommerce.Repository.Entities.ItemQuantityTier})">
            <summary>
            Cập nhật bảng giá theo số lượng sản phẩm/dịch vụ cho bảng giá
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="itemQuantityTiers">Danh sách cấu hình số lượng theo sản phẩm</param>
            <returns>Kết quả cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.SetActivePriceList(System.String,System.String,System.Boolean)">
            <summary>
            Set active status for price list (Thay đổi trạng thái active của bảng giá)
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="priceListId">ID của bảng giá</param>
            <param name="isActive">Trạng thái active (true: kích hoạt, false: vô hiệu hóa)</param>
            <returns>Result set active status</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetItemsByPriceListId(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm theo priceListId
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách sản phẩm với giá đã điều chỉnh</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetItemsNotInAnyPriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm chưa thuộc bất kỳ bảng giá nào 
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetItemQuantityTiers(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm/dịch vụ theo bậc số lượng
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách sản phẩm/dịch vụ theo bậc số lượng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PriceListPartnerController.GetRanksNotInAnyPriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách rank chưa thuộc bất kỳ bảng giá nào 
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách hạng thành viên chưa thuộc bất kỳ bảng giá nào </returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.GetProfile">
            <summary>
            Get Partner profile
            </summary>
            <returns>The result profile of Partner logged</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.UpdateProfile(App.ECommerce.Resource.Dtos.PartnerDto)">
            <summary>
            Update Partner profile
            </summary>
            <param name="model"></param>
            <returns>The result update profile of Partner logged</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.UpdateAvatar(App.ECommerce.Controllers.API.ProfilePartnerController.UploadAvatarPartnerModel)">
            <summary>
            Update avatar for Partner
            </summary>
            <param name="model"></param>
            <returns>The result UpdateAvatar</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.UpdateTokenFCM(App.ECommerce.Resource.Dtos.RefreshTokenDto)">
            <summary>
            Update token FCM for Partner
            </summary>
            <param name="model"></param>
            <returns>The result update token FCM for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.Check(System.String)">
            <summary>
            Check exits phone number for Partner (required format +84)
            </summary>
            <param name="phoneNumber"></param>    
            <returns>Result check Partner exits</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.ResetPass(App.ECommerce.Controllers.API.ProfilePartnerController.ResetPassPartnerModel)">
            <summary>
            Reset pass for Partner (required SendOTP + VerifyOTP has succeeded)
            </summary>
            <param name="model"></param>
            <returns>The result ResetPass for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.GetTypeBusinessList">
            <summary>
            Lấy danh sách loại hình kinh doanh (TypeBusiness)
            </summary>
            <returns>Danh sách các loại hình kinh doanh</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ProfilePartnerController.ChangePassword(App.ECommerce.Resource.Dtos.ChangePasswordDto)">
            <summary>
            Đổi mật khẩu cho Partner đang đăng nhập
            </summary>
            <param name="model"></param>
            <returns>Kết quả đổi mật khẩu</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressPartnerController.CreateShippingAddress(App.ECommerce.Resource.Dtos.ShippingAddressDto)">
            <summary>
            Partner create shipping address (Đối tác tạo địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result Create</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressPartnerController.UpdateShippingAddress(App.ECommerce.Resource.Dtos.ShippingAddressDto)">
            <summary>
            Partner update shipping address (Đối tác cập nhật địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result Update</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressPartnerController.DeleteShippingAddress(System.String)">
            <summary>
            Partner delete shipping address (Đối tác xóa địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="shippingAddressId"></param>
            <returns>The result Delete</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressPartnerController.ListUserShippingAddress(App.ECommerce.Resource.Dtos.RequiredShippingAddressDto,System.Int32,System.Int32)">
            <summary>
            Get list shipping address of user (Lấy danh sách địa chỉ giao hàng của người dùng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListAddress for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopMiniAppPartnerController.UpdateShopMiniApp(App.ECommerce.Controllers.API.ShopMiniAppPartnerController.UpdateShopMiniAppDto)">
            <summary>
             Update Shop Mini App (Cập nhật template mini app cho cửa hàng)
             </summary>
             <param name="model"></param>
             <returns>The result UpdateShopMiniApp</returns>
            POST: api/partner/ShopMiniAppPartner/UpdateShopMiniApp
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopMiniAppPartnerController.CurrentShopMiniApp(System.String)">
            <summary>
             Get current shop mini app (Lấy thông tin template mini app hiện tại của cửa hàng)
             </summary>
             <param name="shopId"></param>
             <returns>The result CurrentShopMiniApp</returns>
            GET: api/partner/ShopMiniAppPartner/CurrentShopMiniApp
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.ListShop(App.ECommerce.Resource.Dtos.SearchDto,System.Int32,System.Int32)">
            <summary>
            Get list shop for Partner (Lấy danh sách cửa hàng của đối tác)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list list shop for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.CreateShop(App.ECommerce.Resource.Dtos.ShopDto)">
            <summary>
            Create Shop (Tạo mới cửa hàng cho đối tác)
            </summary>
            <param name="model"></param>
            <returns>The result CreateShop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.UpdateShop(App.ECommerce.Resource.Dtos.ShopDto)">
            <summary>
            Update Shop (Cập nhật thông tin cửa hàng cho đối tác)
            </summary>
            <param name="model"></param>
            <returns>The result update Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.UpsertShopPolicy(App.ECommerce.Resource.Dtos.ShopPolicyDto)">
            <summary>
            Update Shop (Cập nhật thông tin policy cửa hàng cho đối tác)
            </summary>
            <param name="model"></param>
            <returns>The result update Shop Policy</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.DeleteShop(System.String)">
            <summary>
            Delete Shop (Xóa cửa hàng, chỉ hỗ trợ cho quản trị)
            </summary>
            <param name="shopId"></param>
            <returns>The result delete Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.DetailShop(System.String)">
            <summary>
            Get detail Shop (Lấy thông tin chi tiết cửa hàng)
            </summary>
            <param name="shopId"></param>
            <returns>The result detail Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.UpdateShopDelivery(App.ECommerce.Controllers.API.ShopPartnerController.UpdateShopDeliveryModel)">
            <summary>
            Update Shop Configuration (Cập nhật cấu hình cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result update Shop Configuration</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopPartnerController.UpdateTaxRate(System.String,System.Decimal)">
            <summary>
            Update Shop TaxRate (Cập nhật thuế của cửa hàng)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="taxRate">Phần trăm thuế</param>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopSettingController.CreateShopSetting(App.ECommerce.Resource.Dtos.ShopSettingDto)">
            <summary>
            Create ShopSetting (Tạo mới ShopSetting cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateShopSetting for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopSettingController.DetailShopSettingDto(System.String)">
            <summary>
            Get detail ShopSetting (Lấy thông tin ShopSetting cho cửa hảng)
            </summary>
            <param name="shopId"></param>
            <returns>The result DetailShopSetting for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopSettingController.CreateGithubTag(App.ECommerce.Controllers.API.CreateGithubTagRequest)">
            <summary>
            Create a GitHub tag for the shop's miniapp (CI/CD trigger)
            </summary>
            <param name="model">Payload with shopId, version, branch, githubToken</param>
            <returns></returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopSettingController.GetZaloAccessToken(App.ECommerce.Controllers.API.ShopSettingController.ZaloAccessTokenRequest)">
            <summary>
            Public API: Refresh and get Zalo user access token by miniAppId (no auth, evoSecretKey required)
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.GetList(System.String,System.String)">
            <summary>
            Lấy danh sách cấu hình hóa đơn theo shopId và userId
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="userId">ID người dùng</param>
            <returns>Danh sách cấu hình hóa đơn</returns>
            GET: api/user/taxinvoice/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.Create(App.ECommerce.Resource.Dtos.InputDtos.TaxInvoiceConfigurationDto)">
            <summary>
            Tạo mới cấu hình hóa đơn
            </summary>
            <param name="obj">Thông tin cấu hình hóa đơn</param>
            <returns>Cấu hình hóa đơn đã tạo</returns>
            POST: api/user/taxinvoice
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.Update(App.ECommerce.Resource.Dtos.InputDtos.TaxInvoiceConfigurationDto)">
            <summary>
            Cập nhật cấu hình hóa đơn
            </summary>
            <param name="obj">Thông tin cấu hình hóa đơn cần cập nhật</param>
            <returns>Cấu hình hóa đơn đã cập nhật</returns>
            PUT: api/user/taxinvoice
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.Delete(System.String,System.String)">
            <summary>
            Xóa cấu hình hóa đơn
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID cấu hình hóa đơn</param>
            <returns>Kết quả xóa</returns>
            DELETE: api/user/taxinvoice/{shopId}/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.SetDefault(System.String,System.String,System.String)">
            <summary>
            Đặt cấu hình hóa đơn mặc định
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="userId">ID người dùng</param>
            <param name="id">ID cấu hình hóa đơn</param>
            <returns>Kết quả cập nhật</returns>
            PUT: api/user/taxinvoice/default/{shopId}/{userId}/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.GetBusinessInfo(System.String)">
            <summary>
            Lấy thông tin doanh nghiệp theo mã số thuế
            </summary>
            <param name="taxCode">Mã số thuế</param>
            <returns>Thông tin doanh nghiệp</returns>
            GET: api/user/taxinvoice/business/info/{taxCode}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceController.GetBusinessInfoBySlug(System.String,System.String)">
            <summary>
            Lấy thông tin doanh nghiệp theo mã số thuế và tên
            </summary>
            <param name="taxCode">Mã số thuế</param>
            <param name="name">Tên</param>
            <returns>Thông tin doanh nghiệp</returns>
            GET: api/user/taxinvoice/individual/info/{taxCode}
        </member>
        <member name="T:App.ECommerce.Controllers.API.TransportMethodPartnerController.TransportMethodUpdateModel">
            <summary>
            DTO cho cập nhật phương thức vận chuyển
            </summary>
        </member>
        <member name="T:App.ECommerce.Controllers.API.TransportMethodPartnerController.ToggleTransportMethodModel">
            <summary>
            DTO cho bật/tắt phương thức vận chuyển
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TransportMethodPartnerController.GetTransportMethods(System.String)">
            <summary>
            Lấy danh sách phương thức vận chuyển (Ahamove, JTExpress)
            </summary>
            <param name="shopId">ID của shop</param>
            <returns>Danh sách phương thức vận chuyển</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TransportMethodPartnerController.UpdateTransportMethod(App.ECommerce.Controllers.API.TransportMethodPartnerController.TransportMethodUpdateModel)">
            <summary>
            Cập nhật cấu hình cho phương thức vận chuyển
            </summary>
            <param name="model">Thông tin cập nhật</param>
            <returns>Phương thức vận chuyển đã cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TransportMethodPartnerController.SignUpAhamove(App.ECommerce.Controllers.API.TransportMethodPartnerController.SignUpAhamoveModel)">
            <summary>
            Đăng ký Ahamove cho shop
            </summary>
            <param name="model">body của request</param>
            <returns>Kết quả đăng ký Ahamove</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TransportMethodPartnerController.EnsureDefaultTransportMethods(System.String,System.Collections.Generic.List{App.ECommerce.Repository.Entities.TransportMethod})">
            <summary>
            Đảm bảo tồn tại các phương thức vận chuyển mặc định (Ahamove, JTExpress)
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventController.GetListTriggerEvents">
            <summary>
            Get list trigger events. (Danh sách sự kiện kích hoạt)
            </summary>
            <returns>A list of trigger events</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventController.GetListTriggerEventsByType(System.String,App.ECommerce.Units.Enums.NotificationTypeEnum)">
            <summary>
            Get list trigger events by type. (Danh sách sự kiện kích hoạt theo loại thông báo)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="type">Loại thông báo</param>
            <returns>A list of trigger events matching the specified type.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventController.GetListParameterById(System.String)">
            <summary>
            Get list parameter by trigger event id (Danh sách parameter theo id sự kiện kích hoạt)
            </summary>
            <param name="triggerEventId">ID của sự kiện kích hoạt</param>
            <returns>Danh sách tham số của sự kiện kích hoạt</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventController.GetListTemplate(System.String,System.String)">
            <summary>
            Get list template by trigger event id (Danh sách template theo id sự kiện kích hoạt)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="triggerEventId">ID của sự kiện kích hoạt</param>
            <returns>Danh sách template của sự kiện kích hoạt</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventController.ChangeTemplateActiveFlag(System.String,System.String,System.Boolean)">
            <summary>
            Change Template Active Flag (Thay đổi trạng thái active của template trong trigger event)
            </summary>
            <param name="triggerEventId">ID của sự kiện kích hoạt</param>
            <param name="templateId">ID của template</param>
            <param name="isActive">Trạng thái active mới</param>
            <returns>Kết quả cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TriggerEventHistoryController.GetListSendMessage(App.ECommerce.Resource.Dtos.InputDtos.TriggerEventHistoryFilterDto)">
            <summary>
            Get list history send message by trigger event (Danh sách lịch sử gửi tin nhắn theo sự kiện kích hoạt)
            </summary>
            <returns>Danh sách lịch sử gửi tin nhắn theo sự kiện kích hoạt</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.GetListByShopId">
            <summary>
            Get List Filter Conditon (Danh sách lọc)
            </summary>
            <returns>Danh sách lọc</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.GetListByShopId(App.ECommerce.Resource.Dtos.InputDtos.UserGroupFilterDto)">
            <summary>
            Get List By Shop Id (Lấy danh sách nhóm người dùng theo shop ID)
            </summary>
            <param name="obj">Điều kiện tìm kiếm</param>
            <returns>Danh sách nhóm người dùng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.GetById(System.String,System.String)">
            <summary>
            Get User Group By Id (Lấy thông tin nhóm người dùng theo ID)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="groupId">ID của nhóm người dùng</param>
            <returns>Thông tin nhóm người dùng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.GetListUserByGroupById(App.ECommerce.Resource.Dtos.InputDtos.UserGroupDetailFilterDto)">
            <summary>
            Get List User Group By Id (Lấy danh sách người dùng theo ID nhóm người dùng)
            </summary>
            <param name="obj">Danh sách tham số tìm kiếm</param>
            <returns>Thông tin nhóm người dùng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.Create(App.ECommerce.Resource.Dtos.UserGroupDto)">
            <summary>
            Create User Group (Tạo mới nhóm người dùng)
            </summary>
            <param name="obj">Thông tin nhóm người dùng</param>
            <returns>Nhóm người dùng đã tạo</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.Update(App.ECommerce.Resource.Dtos.UserGroupDto)">
            <summary>
            Update User Group (Cập nhật nhóm người dùng)
            </summary>
            <param name="obj">Thông tin cập nhật</param>
            <returns>Kết quả cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.Delete(System.String,System.String)">
            <summary>
            Delete User Group (Xóa nhóm người dùng)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="groupId">ID của nhóm người dùng</param>
            <returns>Kết quả xóa</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.Filter(App.ECommerce.Resource.Dtos.FilterRequestDto)">
            <summary>
            Filter User (Lọc danh sách người dùng)
            </summary>
            <param name="request">Điều kiện lọc</param>
            <returns>Danh sách người dùng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.ExportUserGroupTemplate">
            <summary>
            Export Excel template for user group import (Xuất file Excel mẫu để nhập dữ liệu người dùng)
            </summary>
            <returns>CSV file with template for user import</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.ImportUserGroup(Microsoft.AspNetCore.Http.IFormFile,App.ECommerce.Resource.Dtos.InputDtos.ImportUserGroupDto)">
            <summary>
            Import users from Xlsx file (Nhập dữ liệu người dùng từ file Xlsx)
            </summary>
            <param name="file">Xlsx file containing user data</param>
            <param name="obj">ID of the shop</param>
            <returns>Result of the import process</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserGroupController.ExportListUser(App.ECommerce.Resource.Dtos.InputDtos.UserGroupDetailFilterDto)">
            <summary>
            Export users to Xlsx file (Xuất dữ liệu người dùng sang file Xlsx)
            </summary>
            <param name="objFilter">Tham số tìm kiếm</param>
            <returns>Result of the import process</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.CreateUser(App.ECommerce.Resource.Dtos.UserDto)">
            <summary>
            Partner create user (Đối tác tạo người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result CreateUser</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.GetUser(System.String)">
            <summary>
            Get detail User (Lấy thông tin chi tiết người dùng)
            </summary>
            <param name="userId"></param>
            <returns>The result UserDetail</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.UpdateUser(App.ECommerce.Resource.Dtos.UserDto)">
            <summary>
            Partner update user (Đối tác cập nhật thông tin người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result UpdateUse</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.ListUser(App.ECommerce.Resource.Dtos.RequiredUserDto,System.Int32,System.Int32)">
            <summary>
            Get list User of shop for Partner (Lấy danh sách người dùng theo cửa hảng của đối tác)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListUser for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.ListUserTag(App.ECommerce.Resource.Dtos.RequiredUserTagDto,System.Int32,System.Int32)">
            <summary>
            Get list tag of user (Lấy danh sách tag của người dùng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListTag for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.DeleteUsers(System.Collections.Generic.List{System.String})">
            <summary>
            Delete users (Xóa nhiều người dùng)
            </summary>
            <param name="userIds"></param>
            <returns>Result ListTag for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.ListUserByUserIds(App.ECommerce.Controllers.API.UserPartnerController.ListUserByUserIdsDto)">
            <summary>
            Get list User by userIds (Lấy danh sách người dùng theo userIds)
            </summary>
            <param name="model"></param>
            <returns>Result ListUserByUserIds for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.UpdateUserInfo(App.ECommerce.Controllers.API.UserPartnerController.UpdateUserInfoDto)">
            <summary>
            Update user info (Cập nhật thông tin khách hàng)
            </summary>
            <param name="model"></param>
            <returns>Return result of  UpdateUserInfo</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.ExportUserTemplate">
            <summary>
            Export Excel template for user import (Xuất file Excel mẫu để nhập dữ liệu người dùng)
            </summary>
            <returns>CSV file with template for user import</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.UserPartnerController.ImportUsers(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
            Import users from Xlsx file (Nhập dữ liệu người dùng từ file Xlsx)
            </summary>
            <param name="file">Xlsx file containing user data</param>
            <param name="shopId">ID of the shop</param>
            <returns>Result of the import process</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.CreateVoucher(App.ECommerce.Resource.Dtos.VoucherDto)">
            <summary>
            Create a new voucher
            </summary>
            <param name="voucherDto">Voucher information</param>
            <returns>Created voucher</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.GetVoucher(System.String)">
            <summary>
            Get a voucher by ID
            </summary>
            <param name="voucherId">Voucher ID</param>
            <returns>Voucher information</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.ListVouchers(App.ECommerce.Resource.Dtos.FilterVoucherDto)">
            <summary>
            Get list of vouchers with filtering and pagination
            </summary>
            <returns>Paginated list of vouchers</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.UpdateVoucher(App.ECommerce.Resource.Dtos.VoucherDto)">
            <summary>
            Update an existing voucher
            </summary>
            <param name="voucherDto">Updated voucher information</param>
            <returns>Updated voucher</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.DeleteVouchers(System.Collections.Generic.List{System.String})">
            <summary>
            Delete vouchers
            </summary>
            <param name="voucherIds">List of voucher IDs to delete</param>
            <returns>Success status</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherPartnerController.GetVoucherDetails(System.String,App.ECommerce.Resource.Model.Paging)">
            <summary>
            Get voucher details by voucher ID (support paging or all)
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.WarehousePartnerController.ListWarehouse(App.ECommerce.Controllers.API.WarehousePartnerController.FilterWarehouseDto,System.Int32,System.Int32)">
            <summary>
            Get list warehouse for shop (Danh sách kho cho cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list ListWarehouse for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloController.GetAuthorizationURL(System.String)">
            <summary>
            Get URL Authorization Zalo(URL ủy quyền Zalo)
            </summary>
            <param name="shopId"></param>
            <returns>Result URL Authorization</returns>
            POST: api/partner/zalo/callback
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloOAuthController.Webhook(App.ECommerce.Repository.Entities.Zalo_Webhook)">
            <summary>
            Handle webhook events from Zalo (Xử lý các sự kiện webhook từ Zalo)
            </summary>
            <returns>Kết quả xử lý webhook</returns>
            POST: api/partner/zalooauth/webhook 
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloOAuthController.CallBack(System.String,System.String,System.String)">
            <summary>
            Handles the callback from Zalo after user authorization OA(Xử lý callback từ Zalo sau khi người dùng ủy quyền OA)
            </summary>
            <param name="oa_id"></param>
            <param name="code"></param>
            <param name="state"></param>
            <returns>Result Message Authorization</returns>
            POST: api/partner/zalo/callback
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.GetZNSemplates(System.String,System.String,System.String)">
            <summary>
            Lấy danh sách template ZNS theo shopId
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="templateName">Tên mẫu tin ZNS Zalo</param>
            <param name="templateId">ID mẫu tin ZNS Zalo</param>
            <returns>Result List Template ZNS</returns>
            GET: api/partner/zalozns/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.GetEnabledTemplatesByShopId(System.String)">
            <summary>
            Lấy danh sách template ZNS theo shopId
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <returns>Result List Template ZNS</returns>
            GET: api/partner/zalozns/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.GetInfoTemplate(System.String,System.String)">
            <summary>
            Lấy thông tin chi tiết template ZNS theo id
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID template</param>
            <returns>Result Template ZNS</returns>
            GET: api/partner/zalozns/info/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.ExportTemplateZNS(System.String,System.String)">
            <summary>
            Export file template Excel by ZnsId (Xuất file template Excel)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID template</param>
            <returns>Result Template ZNS</returns>
            GET: api/partner/zalozns/info/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.AddZNSemplate(App.ECommerce.Resource.Dtos.ZaloZNSTemplateDto)">
            <summary>
            Thêm mới template ZNS
            </summary>
            <param name="obj"></param>
            <returns>Result Template ZNS</returns>
            POST: api/partner/zalozns/
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.UpdateZNSemplate(System.String,System.String,App.ECommerce.Units.Enums.Zalo.ZNSTemplateTypeEnum)">
            <summary>
            Cập nhật template ZNS
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="znsId">ID template</param>
            <param name="templateType">Loại template</param>
            <returns>Result Template ZNS</returns>
            PUT: api/partner/zalozns/{shopId}/{znsId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.DeleteZNSemplate(System.String,System.String)">
            <summary>
            Xóa template ZNS theo id
            </summary>
            <param name="shopId"></param>
            <param name="id"></param>
            <returns>Result true/false</returns>
            DELETE: api/partner/zalozns/
        </member>
        <member name="M:App.ECommerce.Controllers.API.ZaloZNSController.GetZaloZNSTemplates(System.String)">
            <summary>
            Lấy danh sách template ZNS từ Zalo
            </summary>
            <param name="shopId"></param>
            <returns>Result List Template ZNS</returns>
            GET: api/partner/zalozns/load/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_ButtonController.ListButtonAction">
            <summary>
            Retrieves a list of all available Zalo button actions.
            </summary>
            <returns>A list of Zalo button actions.</returns>
            <response code="200">Returns the list of Zalo button actions.</response>
            <response code="401">If the user is not authenticated or authorized.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.CreateMessageType(Zalo_MessageTypeRequestDto)">
            <summary>
            Creates a new Zalo message type.
            </summary>
            <param name="request">The request containing the Zalo message type details.</param>
            <returns>The created Zalo message type ID.</returns>
            <response code="200">Returns the newly created Zalo message type ID.</response>
            <response code="400">If the request is invalid.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.ListMessageTypes">
            <summary>
            Retrieves a list of Zalo message types.
            </summary>
            <returns>A list of Zalo message types with metadata.</returns>
            <response code="200">Returns the list of Zalo message types</response>
            <response code="401">If the user is not authorized.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.GetMessageTypeDetail(System.Guid)">
            <summary>
            Retrieves the details of a Zalo message type by its ID.
            </summary>
            <param name="id">The ID of the Zalo message type to retrieve.</param>
            <returns>The Zalo message type details.</returns>
            <response code="200">Returns the Zalo message type details.</response>
            <response code="401">If the user is not authorized.</response>
            <response code="404">If the message type is not found.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.UpdateMessageType(System.Guid,Zalo_MessageTypeRequestDto)">
            <summary>
            Updates an existing Zalo message type.
            </summary>
            <param name="id">The ID of the Zalo message type to update.</param>
            <param name="request">The updated Zalo message type details.</param>
            <returns>The updated Zalo message type ID.</returns>
            <response code="200">Returns the updated Zalo message type ID.</response>
            <response code="400">If the request is invalid.</response>
            <response code="401">If the user is not authorized.</response>
            <response code="404">If the message type is not found.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.DeleteMessageType(System.Guid)">
            <summary>
            Deletes a Zalo message type by its ID.
            </summary>
            <param name="id">The ID of the Zalo message type to delete.</param>
            <returns>No content if successful.</returns>
            <response code="200">Indicates the message type was successfully deleted.</response>
            <response code="401">If the user is not authorized.</response>
            <response code="404">If the message type is not found.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_MessageTypeController.GetMessageTypeByTemplateType(App.ECommerce.Units.Enums.Zalo_Template_Type_Enum)">
            <summary>
            Retrieves Zalo message types by template type.
            </summary>
            <param name="templateType">The template type to filter message types by.</param>
            <returns>A list of Zalo message types matching the specified template type.</returns>
            <response code="200">Returns the list of Zalo message types for the specified template type.</response>
            <response code="400">If the template type is invalid.</response>
            <response code="401">If the user is not authorized.</response>
            <response code="404">If no message types are found for the template type.</response>
            <response code="500">If an internal server error occurs.</response>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.ListTemplates(App.ECommerce.Resource.Dtos.Zalo_TemplateSearchDto,System.Int32,System.Int32)">
            <summary>
            Retrieves a paginated list of Zalo templates based on search criteria.
            </summary>
            <param name="model">The search criteria for filtering Zalo templates.</param>
            <param name="skip">The number of records to skip (default is 0).</param>
            <param name="limit">The maximum number of records to return (default is 99).</param>
            <returns>A paginated list of Zalo templates with additional metadata.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.GetTemplateDetail(System.String,System.String)">
            <summary>
            Retrieves the details of a Zalo template by its ID.
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID template Zalo UID</param>
            <returns>The Zalo template details.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.CreateTemplate(App.ECommerce.Resource.Dtos.Zalo_TemplateRequestDto)">
            <summary>
            Creates a new Zalo UID template (Tạo template Zalo UID)
            </summary>
            <param name="objRequest">The request containing the Zalo template details.</param>
            <returns>The created Zalo template.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.UpdateTemplate(App.ECommerce.Resource.Dtos.Zalo_TemplateRequestDto)">
            <summary>
            Updates an existing Zalo template.
            </summary>
            <param name="objRequest">The updated Zalo template details.</param>
            <returns>The updated Zalo template.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.DeleteTemplate(System.String,System.String)">
            <summary>
            Deletes a Zalo template by its ID. (Xoá template Zalo UID theo ID)
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID template</param>
            <returns>No content if successful.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_TemplateController.ChangeTemplateStatus(System.String,System.String,App.ECommerce.Units.Enums.Zalo_Template_Status_Enum)">
            <summary>
            Changes the status of an existing Zalo template.
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">The ID of the Zalo template to update status.</param>
            <param name="status">The new status to set (Active/Inactive).</param>
            <returns>The updated template ID and new status.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_WebHookController.GetListByAppId(System.String)">
            <summary>
            Get list of webhooks by AppId (Lấy danh sách webhook theo AppId)
            </summary>
            <param name="appId">ID của ứng dụng Zalo</param>
            <returns>Danh sách webhook của ứng dụng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.Zalo_WebHookController.GetListByUserIdByApp(System.String,System.String)">
            <summary>
            Get list of webhooks by UserIdByApp (Lấy danh sách webhook theo UserIdByApp)
            </summary>
            <param name="appId">ID ứng dụng</param>
            <param name="userIdByApp">ID của người dùng trong ứng dụng</param>
            <returns>Danh sách webhook của người dùng</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherController.GetVoucherByCode(System.String)">
            <summary>
            Api public để lấy thông tin cơ bản của voucher theo mã voucher
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AdvertiseUserController.GetAdvertise(System.String,System.Int32,System.Int32)">
            <summary>
            Get list active Advertise for Shop (Lấy danh sách popup quảng cáo đang hoạt động cho cửa hàng)
            </summary>
            <param name="shopId">ID of the shop</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Maximum number of records to return</param>
            <returns>Result list active Advertise for Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticleUserController.ListArticleCategory(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list ArticleCategory for shop (Danh sách danh mục bài viết cho cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListArticleCategory for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ArticleUserController.ListArticle(App.ECommerce.Resource.Dtos.RequiredShopDto,System.Int32,System.Int32)">
            <summary>
            Get list Article for shop (Danh sách bài viết của cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListArticle for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.Register(App.ECommerce.Resource.Dtos.UserRegistryDto)">
            <summary>
            Registry for user
            </summary>
            <param name="model"></param>    
            <returns>Result registry for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.Login(App.ECommerce.Controllers.API.AuthUserController.LoginUserModel)">
            <summary>
            Login for user, require password encode md5
            </summary>
            <param name="model"></param>
            <returns>Result login for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.RefreshToken(System.String)">
            <summary>
            Refresh token for user logged
            </summary>
            <param name="refreshToken"></param>
            <returns>New token for user logged</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.Logout(System.String,System.String)">
            <summary>
            Logout for user
            </summary>
            <param name="refreshToken"></param>
            <param name="tokenFCM"></param>
            <returns>Result Logout for User</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.SetPass(App.ECommerce.Controllers.API.AuthUserController.SetPassUserModel)">
            <summary>
            set pass for user
            </summary>
            <param name="model"></param>
            <returns>The result SetPass for User</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.AuthUserController.ActiveAccount(App.ECommerce.Resource.Dtos.UserRegistryDto)">
            <summary>
            Active for user
            </summary>
            <param name="model"></param>    
            <returns>Active account for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.BranchUserController.ListBranch(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get list Branch for User (Lấy danh sách điểm bán hàng)
            </summary>
             <param name="shopId"></param>
             <param name="search"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list Branch for Partner</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartUserController.GetCart">
            <summary>
            Get detail cart for user (Lấy thông tin chi tiết giỏ hàng cho user)
            </summary>
            <returns>The result GetCart for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartUserController.CreateOrUpdateCart(App.ECommerce.Resource.Dtos.CreateOrUpdateCartDto)">
            <summary>
            Create or update cart (Tạo mới hoặc cập nhật giỏ hàng cho user)
            </summary>
            <param name="model"></param>
            <returns>The result CreateOrUpdateCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CartUserController.EstimatePointCart">
            <summary>
            Kiểm tra điểm khả dụng mua hàng của user
            </summary>
            <returns>The result GetMaxExchangePointByCart for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.CategoryUserController.ListCategory(App.ECommerce.Resource.Dtos.InputDtos.CategoryFilterDto)">
            <summary>
            Get detail items for shop (Lấy thông tin chi sản phẩm hoặc dịch vụ, không phân nhóm theo biến thể)
            </summary>
            <param name="filter"></param>
            <returns>The result DetailItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemOptionUserController.GetItemOptionsGroup(System.Collections.Generic.List{System.String})">
            <summary>
            Get list item option groups include item options by group ids
            </summary>
            <param name="itemOptionGroupIds">item option group ids</param>
            <returns>return list option groups include item option</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsUserController.ListItems(App.ECommerce.Resource.Dtos.InputDtos.ItemFilterDto)">
            <summary>
            Get list items for shop (Danh sách sản phẩm hoặc dịch vụ cho cửa hàng)
            </summary>
            <param name="model"></param>
            <returns>Result ListItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsUserController.DetailGroupItems(System.String)">
            <summary>
            Get detail items group variant for shop (Lấy thông tin chi tiết sản phẩm hoặc dịch vụ nhóm theo biến thể)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DetailGroupItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsUserController.DetailItems(System.String)">
            <summary>
            Get detail items for shop (Lấy thông tin chi sản phẩm hoặc dịch vụ, không phân nhóm theo biến thể)
            </summary>
            <param name="itemsCode"></param>
            <returns>The result DetailItems for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ItemsUserController.DetailItem(System.String)">
            <summary>
            Get product by id (Lấy chi tiết sản phẩm theo id sản phẩm)
            </summary>
            <param name="itemId"></param>
            <returns>The result DetailItem</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MeController.IncomeHistory(System.String,System.Int32,System.Int32)">
            <summary>
            Get GetIncomeHistory. (Lấy thông tin lịch sử cộng điểm)
            </summary>
            <param name="shopId">Number of records to skip</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to take</param>
            <returns>Get Income History</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MeController.SpendingHistory(System.String,System.Int32,System.Int32)">
            <summary>
            Get GetSpendingHistory. (Lấy thông tin lịch sử trừ điểm)
            </summary>
            <param name="shopId">Number of records to skip</param>
            <param name="skip">Number of records to skip</param>
            <param name="limit">Number of records to take</param>
            <returns>Get Spending History</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelUserController.GetListMembershipLevel(System.String,System.Int32,System.Int32)">
            GET: api/user/membershipleveluser/ranks/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.MembershipLevelUserController.GetConfigPoint(System.String)">
            <summary>
            Cấu hình điểm thưởng cho người dùng
            </summary>
            <param name="shopId">Id cửa hàng</param>
            <returns>Thông tin doanh nghiệp</returns>
            GET: api/user/membershipleveluser/configpoint/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.CreateOrder">
            <summary>
            Create order (Tạo mới đơn hàng, tạo mới transactionId is empty)
            </summary>
            <returns>The result CreateOrder for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.OrderDetail(System.String)">
            <summary>
            User Get Order Detail (Người dùng lấy thông tin chi tiết đơn hàng)
            </summary>
            <param name="orderId"></param>
            <returns>The result OrderDetail</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.ListOrder(App.ECommerce.Controllers.API.OrderUserController.ListOrderUserDto,System.Int32,System.Int32)">
            <summary>
            Get list order for User (Lấy danh sách đơn hàng của user)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result list order for User</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.UpdateOrder(App.ECommerce.Controllers.API.OrderUserController.UserUpdateOrderInfoDto)">
            <summary>
            User update order info (User cập nhật trạng thái đơn hàng)
            </summary>
            <param name="model"></param>
            <returns>The result Update</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.CreateMac(App.ECommerce.Controllers.API.OrderUserController.CreateMacRequestDto)">
            <summary>
            Create Mac (Tạo mã bảo mật cho thanh toán)
            </summary>
            <param name="model"></param>
            <returns>The result CreateMac</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.ZaloNotify(App.ECommerce.Controllers.API.OrderUserController.ZaloNotifyRequestDto)">
            <summary>
            Tạo API thanh toán zalo
            </summary>
            <param name="model"></param>
            <returns>The result ZaloNotify</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.CheckoutCallback(App.ECommerce.Controllers.API.OrderUserController.ZaloCheckoutCallBackRequestDto)">
            <summary>
            Checkout callback
            </summary>
            <param name="model"></param>
            <returns>The result CheckoutCallback</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.AhamoveCallBack">
            <summary>
            Ahamove callback
            </summary>
            <param name="model"></param>
            <returns>The result CheckoutCallback</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.ZaloVnpayIPN">
            <summary>
            Mục đích của webhook này là cung cấp cho đối tác đầy đủ thông tin cần thiết để hoàn tất hồ sơ kiểm thử trong môi trường Sandbox (SIT Test) theo yêu cầu của VNPay.
            </summary>
            <returns>The result ZaloVnpayIPN</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.JTExpressCallBack">
            <summary>
            JTExpress CallBack
            </summary>
            <returns>The result JTExpressCallBack</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.OrderUserController.GetTotalOrderStatus(System.String)">
            <summary>
            Toltal Order Status (Tổng số lượng đơn hàng theo trạng thái)
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.PaymentUserController.ListPayment(App.ECommerce.Controllers.API.PaymentUserController.RequiredShopDto)">
            <summary>
            Get list Payment for shop (Danh sách phương thức thanh toán của cửa hàng)
            </summary>
            <param name="model"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListPayment for shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressUserController.CreateShippingAddress(App.ECommerce.Resource.Dtos.ShippingAddressDto)">
            <summary>
            User create shipping address (tạo địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result Create</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressUserController.UpdateShippingAddress(App.ECommerce.Resource.Dtos.ShippingAddressDto)">
            <summary>
            User update shipping address (cập nhật địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="model"></param>
            <returns>The result Update</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressUserController.DeleteShippingAddress(System.String)">
            <summary>
            User delete shipping address (xóa địa chỉ giao hàng cho người dùng)
            </summary>
            <param name="shippingAddressId"></param>
            <returns>The result Delete</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShippingAddressUserController.ListUserShippingAddress(System.String,System.Int32,System.Int32)">
            <summary>
            Get list shipping address of user (Lấy danh sách địa chỉ giao hàng của người dùng)
            </summary>
            <param name="search"></param>
            <param name="skip"></param>
            <param name="limit"></param>
            <returns>Result ListAddress for user</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopInfoController.GetShopInfoByDomain(System.String)">
            <summary>
            Public API để lấy thông tin cửa hàng theo domain
            </summary>
            <param name="domain">Domain name của cửa hàng</param>
            <returns>Thông tin cửa hàng bao gồm tên, slogan và logo</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopMiniAppUserController.CurrentShopMiniApp(System.String)">
            <summary>
             Get current shop mini app (Lấy thông tin template mini app hiện tại của cửa hàng)
             </summary>
             <param name="shopId"></param>
             <returns>The result CurrentShopMiniApp</returns>
            GET: api/partner/ShopMiniAppUser/CurrentShopMiniApp
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopUserController.GetShopId(System.String)">
            <summary>
            Get shop id
            </summary>
            <param name="miniAppId"></param>
            <returns>The result GetShopId for Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopUserController.GetShopIdFromDomain(System.String)">
            <summary>
            Get shop id from domain
            </summary>
            <param name="domain"></param>
            <returns>The result GetShopId for Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopUserController.GetThemeShop(System.String)">
            <summary>
            Get theme for Shop
            </summary>
            <param name="shopId"></param>
            <returns>The result theme for Shop</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopUserController.GetShopPolicy(System.String)">
            <summary>
            Get Shop Policy
            </summary>
            <param name="shopId"></param>
            <returns>The result Shop Policy</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.ShopUserController.GetShopInfo(System.String)">
            <summary>
            Get Shop Info
            </summary>
            <param name="shopId"></param>
            <returns>The result Shop Info</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.GetList(System.String,System.String)">
            <summary>
            Lấy danh sách cấu hình hóa đơn theo shopId và userId
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="userId">ID người dùng</param>
            <returns>Danh sách cấu hình hóa đơn</returns>
            GET: api/user/taxinvoice/{shopId}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.Create(App.ECommerce.Resource.Dtos.InputDtos.TaxInvoiceConfigurationDto)">
            <summary>
            Tạo mới cấu hình hóa đơn
            </summary>
            <param name="obj">Thông tin cấu hình hóa đơn</param>
            <returns>Cấu hình hóa đơn đã tạo</returns>
            POST: api/user/taxinvoice
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.Update(App.ECommerce.Resource.Dtos.InputDtos.TaxInvoiceConfigurationDto)">
            <summary>
            Cập nhật cấu hình hóa đơn
            </summary>
            <param name="obj">Thông tin cấu hình hóa đơn cần cập nhật</param>
            <returns>Cấu hình hóa đơn đã cập nhật</returns>
            PUT: api/user/taxinvoice
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.Delete(System.String,System.String)">
            <summary>
            Xóa cấu hình hóa đơn
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="id">ID cấu hình hóa đơn</param>
            <returns>Kết quả xóa</returns>
            DELETE: api/user/taxinvoice/{shopId}/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.SetDefault(System.String,System.String,System.String)">
            <summary>
            Đặt cấu hình hóa đơn mặc định
            </summary>
            <param name="shopId">ID cửa hàng</param>
            <param name="userId">ID người dùng</param>
            <param name="id">ID cấu hình hóa đơn</param>
            <returns>Kết quả cập nhật</returns>
            PUT: api/user/taxinvoice/default/{shopId}/{userId}/{id}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.GetBusinessInfo(System.String)">
            <summary>
            Lấy thông tin doanh nghiệp theo mã số thuế
            </summary>
            <param name="taxCode">Mã số thuế</param>
            <returns>Thông tin doanh nghiệp</returns>
            GET: api/user/taxinvoice/business/info/{taxCode}
        </member>
        <member name="M:App.ECommerce.Controllers.API.TaxInvoiceConfigurationController.GetBusinessInfoBySlug(System.String,System.String)">
            <summary>
            Lấy thông tin doanh nghiệp theo mã số thuế và tên
            </summary>
            <param name="taxCode">Mã số thuế</param>
            <param name="name">Tên</param>
            <returns>Thông tin doanh nghiệp</returns>
            GET: api/user/taxinvoice/individual/info/{taxCode}
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.GetListVoucherByUserId(App.ECommerce.Resource.Model.Paging,System.String)">
            <summary>
            Api lấy ra list voucher trong kho của người dùng
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.GetListVoucherByCart(App.ECommerce.Resource.Dtos.CartDto)">
            <summary>
            Api lấy ra list voucher thoả mãn giỏ hàng
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.Redeem(System.String)">
            <summary>
            Api để người dùng thu thập voucher
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.RedeemPointVoucher(System.String)">
            <summary>
            Api để người dùng sử dụng voucher điểm
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.GetVoucher(System.String)">
            <summary>
            Api để lấy thông tin chi tiết voucher
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.API.VoucherUserController.GetVoucherByCode(System.String)">
            <summary>
            Api để lấy thông tin chi tiết voucher theo mã voucher (yêu cầu authentication)
            Trả về thông tin chi tiết + trạng thái sở hữu của user
            </summary>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.MinimumVersion">
            <summary>
            Minimum version require (Yêu cầu phiên bản tối thiểu)
            </summary>
            <returns>The result minimum version require</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.ToolForTelegram(Telegram.Bot.Types.Update,App.Base.Services.BotService.TelegramService,System.Threading.CancellationToken)">
            <summary>
            Tool For Telegram
            </summary>
            <returns>The result Tool For Telegram</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.UploadLogo(App.ECommerce.Controllers.SystemController.UploadLogoModel)">
            <summary>
            Upload logo
            </summary>
            <param name="model"></param>
            <returns>The result upload logo</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.DeleteLogo(App.ECommerce.Controllers.SystemController.DeleteLogoModel)">
            <summary>
            Delete logo
            </summary>
            <param name="model"></param>
            <returns>The result delete logo</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.Encrypt(App.ECommerce.Controllers.SystemController.EncryptDataModel)">
            <summary>
            Encrypt data
            </summary>
            <returns>The data encrypt.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.Decrypt(App.ECommerce.Controllers.SystemController.EncryptDataModel)">
            <summary>
            Decrypt data
            </summary>
            <returns>The data decrypt.</returns>
        </member>
        <member name="M:App.ECommerce.Controllers.SystemController.TestSendTelegram(App.ECommerce.Controllers.SystemController.SendTelegram,App.Base.Services.BotService.TelegramService)">
            <summary>
            Test send telegram
            </summary>
            <returns>Result test send telegram.</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.MapPaymentStatusFromNhanhWebhook(System.String,System.Nullable{System.Double},System.Double)">
            <summary>
            Map trạng thái thanh toán từ NhanhVN webhook
            </summary>
            <param name="status">Trạng thái đơn hàng từ NhanhVN</param>
            <param name="moneyTransfer">Tiền chuyển khoản</param>
            <param name="moneyDeposit">Tiền đặt cọc</param>
            <returns>Trạng thái thanh toán</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetNhanhProductsAsync(System.String,System.Int32)">
            <summary>
            Lấy danh sách sản phẩm từ Nhanh.vn API
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="page">Trang hiện tại (mặc định: 1)</param>
            <returns>Response chứa danh sách sản phẩm và thông tin phân trang từ Nhanh.vn</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetAttributeNameByIndex(System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductAttributeDto},System.Int32)">
            <summary>
            Lấy attribute name theo index từ cấu trúc attributes của Nhanh Webhook
            </summary>
            <param name="attributes">Danh sách attributes từ Nhanh Webhook</param>
            <param name="index">Index của attribute cần lấy (0, 1, 2)</param>
            <returns>Attribute name hoặc null nếu không tìm thấy</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetAttributeValueByIndex(System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductAttributeDto},System.Int32)">
            <summary>
            Lấy attribute value theo index từ cấu trúc attributes của Nhanh Webhook
            </summary>
            <param name="attributes">Danh sách attributes từ Nhanh Webhook</param>
            <param name="index">Index của attribute cần lấy (0, 1, 2)</param>
            <returns>Attribute value hoặc null nếu không tìm thấy</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetAllVariantsOfParent(System.String,System.Int32)">
            <summary>
            Lấy tất cả variants của cùng 1 parent product từ Nhanh.vn
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="parentId">ID của parent product</param>
            <returns>Danh sách tất cả variants của parent</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetNhanhProductByIdAsync(System.String,System.Int32)">
            <summary>
            Lấy thông tin 1 sản phẩm theo ID từ Nhanh.vn API
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="productId">ID của sản phẩm cần lấy</param>
            <returns>Thông tin sản phẩm từ Nhanh.vn</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.HandleVariantProduct(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Xử lý sản phẩm biến thể (parentId > 0)
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.CreateVariantItemFromNhanhData(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto},System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Tạo variant item từ dữ liệu Nhanh
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetAllImagesFromVariants(System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto})">
            <summary>
            Lấy tất cả images từ các variants của cùng parent
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetVariantImage(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto})">
            <summary>
            Lấy VariantImage cho variant hiện tại
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.HasSameFirstAttributeValue(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto,System.String)">
            <summary>
            Kiểm tra xem variant có cùng giá trị attribute đầu tiên không
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetNhanhParentProductsAsync(System.String,System.Int32)">
            <summary>
            Lấy danh sách parent products (sản phẩm cha có biến thể) từ Nhanh.vn API
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="page">Trang hiện tại (mặc định: 1)</param>
            <returns>Response chứa danh sách parent products và thông tin phân trang từ Nhanh.vn</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.SyncNhanhProductFromWebhookWithParentCache(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductCategoryDto},System.Collections.Generic.Dictionary{System.Int32,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto})">
            <summary>
            Đồng bộ sản phẩm từ Nhanh webhook với parent products cache
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.HandleVariantProductWithCache(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.Dictionary{System.Int32,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto})">
            <summary>
            Xử lý sản phẩm biến thể với parent products cache
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.HandleStandaloneProduct(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Xử lý sản phẩm độc lập (parentId = -1, không có biến thể)
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.CreateStandaloneItemFromNhanhData(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Tạo standalone item từ dữ liệu Nhanh
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.UpdateExistingStandaloneProduct(App.ECommerce.Repository.Entities.Items,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Cập nhật existing standalone product từ dữ liệu Nhanh
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.CreateImageListFromProductData(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto)">
            <summary>
            Tạo danh sách images từ product data
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.CreateVariantImageFromProductData(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto)">
            <summary>
            Tạo VariantImage từ product data
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.MapCarrierToTransportService(System.String)">
            <summary>
            Map carrier name/code từ Nhanh.vn sang TypeTransportService
            </summary>
            <param name="carrierName">Tên hãng vận chuyển từ Nhanh.vn</param>
            <param name="carrierCode">Mã hãng vận chuyển từ Nhanh.vn</param>
            <returns>TypeTransportService tương ứng hoặc null nếu không map được</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.GetNhanhOrdersAsync(System.String,System.Int32)">
            <summary>
            Lấy danh sách đơn hàng từ Nhanh.vn API
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="page">Trang hiện tại (mặc định: 1)</param>
            <returns>Response chứa danh sách đơn hàng và thông tin phân trang từ Nhanh.vn</returns>
        </member>
        <member name="M:App.ECommerce.Helpers.NhanhHelper.UpdateExistingVariantItem(App.ECommerce.Repository.Entities.Items,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto},System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Cập nhật existing variant item từ dữ liệu Nhanh
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.SyncServiceHelper.FindCategoryByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm category theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.SyncServiceHelper.FindItemByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm item theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.SyncServiceHelper.FindOrderByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm order theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.Interface.ISyncServiceHelper.FindCategoryByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm category theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.Interface.ISyncServiceHelper.FindItemByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm item theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.Interface.ISyncServiceHelper.FindOrderByExternalId(System.String,App.ECommerce.Units.Enums.SyncServiceEnum)">
            <summary>
            Tìm order theo external ID với switch case cho từng service
            </summary>
        </member>
        <member name="M:App.ECommerce.Helpers.PasswordValidator.Validate(System.String,System.Func{System.String,System.String})">
            <summary>
            </summary>
            <param name="password">Mật khẩu cần kiểm tra</param>
            <param name="localizer">Hàm lấy thông báo theo key (có thể truyền null nếu không cần)</param>
            <returns>Danh sách lỗi, rỗng nếu hợp lệ</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.AffiliateFlow.ProcessCommissionPayments">
            <summary>
            Xử lý thanh toán hoa hồng cho tất cả shop đang hoạt động.
            Nếu shop có PaymentDue là ngày hôm nay thì thực hiện thanh toán hoa hồng (Paid),
            ngược lại chỉ tính toán và lưu trạng thái Unpaid. Tránh tạo bản ghi trùng lặp bằng cách kiểm tra trạng thái hiện tại.
            </summary>
            <returns></returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.AffiliateFlow.ApprovePaymentForCommission(System.String,System.Int32,System.Int32,App.ECommerce.Resource.Enums.AffiliationEnum.PaymentStatus)">
            <summary>
            Xử lý thanh toán hoa hồng cho một shop cụ thể
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="month">Tháng</param>
            <param name="year">Năm</param>
            <param name="paymentStatus">Trạng thái thanh toán</param>
            <returns></returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.OrderFlow.SyncOrderToExternalServices(App.ECommerce.Repository.Entities.Order)">
            <summary>
            Đồng bộ đơn hàng lên các external services đã được cấu hình
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CalculatePriceItemWithPriceList(App.ECommerce.Repository.Entities.Items,System.String,System.String)">
            <summary>
            Tính toán giá với PriceList cho một item
            </summary>
            <param name="items">Item cần tính toán giá</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>Item với giá đã được điều chỉnh theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CalculatePriceWithPriceList(App.ECommerce.Resource.Dtos.ItemsGroupByDto,System.String,System.String,System.String)">
            <summary>
            Tính toán giá với PriceList cho một item
            </summary>
            <param name="itemsGroupBy">Item cần tính toán giá</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>Item với giá đã được điều chỉnh theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CalculateListItemWithPriceList(System.Collections.Generic.List{App.ECommerce.Resource.Dtos.ItemsGroupByDto},System.String,System.String)">
            <summary>
            Tính toán giá với PriceList cho danh sách items
            </summary>
            <param name="itemsGroupByList">Danh sách items cần tính toán giá</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>Danh sách items với giá đã được điều chỉnh theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CalculateAdjustedPrice(System.Int64,System.String,System.Collections.Generic.List{App.ECommerce.Repository.Entities.PriceList},System.String,System.String)">
            <summary>
            Tính toán giá đã được điều chỉnh dựa trên các PriceList áp dụng
            </summary>
            <param name="originalPrice">Giá gốc của item</param>
            <param name="item">Item cần tính toán</param>
            <param name="activePriceLists">Danh sách các PriceList đang hoạt động</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>Giá đã được điều chỉnh theo các PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CalculateItemsAdjustedPrice(System.Int64,App.ECommerce.Repository.Entities.Items,System.Collections.Generic.List{App.ECommerce.Repository.Entities.PriceList},System.String,System.String)">
            <summary>
            Tính toán giá đã được điều chỉnh dựa trên các PriceList áp dụng
            </summary>
            <param name="originalPrice">Giá gốc của item</param>
            <param name="item">Item cần tính toán</param>
            <param name="activePriceLists">Danh sách các PriceList đang hoạt động</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>Giá đã được điều chỉnh theo các PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.IsPriceListApplicable(App.ECommerce.Repository.Entities.PriceList,System.String,System.String,System.String)">
            <summary>
            Kiểm tra xem PriceList có áp dụng được cho item, user và branch cụ thể hay không
            </summary>
            <param name="priceList">PriceList cần kiểm tra</param>
            <param name="itemsId">Item cần áp dụng PriceList</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>True nếu PriceList có thể áp dụng, False nếu không</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.IsPriceListApplicable(App.ECommerce.Repository.Entities.PriceList,App.ECommerce.Repository.Entities.Items,System.String,System.String)">
            <summary>
            Kiểm tra xem PriceList có áp dụng được cho item, user và branch cụ thể hay không
            </summary>
            <param name="priceList">PriceList cần kiểm tra</param>
            <param name="item">Item cần áp dụng PriceList</param>
            <param name="userId">ID của user (có thể null)</param>
            <param name="branchId">ID của branch (có thể null)</param>
            <returns>True nếu PriceList có thể áp dụng, False nếu không</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.ApplyPriceListAdjustment(System.Int64,App.ECommerce.Repository.Entities.PriceList)">
            <summary>
            Áp dụng điều chỉnh giá theo PriceList
            </summary>
            <param name="currentPrice">Giá hiện tại</param>
            <param name="priceList">PriceList cần áp dụng</param>
            <returns>Giá sau khi áp dụng điều chỉnh</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.ValidateItemConflicts(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Kiểm tra sản phẩm đã tồn tại trong bảng giá khác chưa
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="itemIds">Danh sách ID sản phẩm/dịch vụ cần kiểm tra</param>
            <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
            <returns>Danh sách xung đột sản phẩm/dịch vụ</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.ValidateRankConflicts(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Kiểm tra rank đã tồn tại trong bảng giá khác chưa
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="rankIds">Danh sách ID rank cần kiểm tra</param>
            <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
            <returns>Danh sách xung đột rank</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.FilterAndSearchItems(System.Collections.Generic.List{App.ECommerce.Repository.Entities.Items},App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lọc và tìm kiếm items theo filter
            </summary>
            <param name="items">Danh sách items cần lọc</param>
            <param name="filter">Filter để lọc items</param>
            <returns>Danh sách items đã được lọc</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.CreateItemsPriceListDto(App.ECommerce.Repository.Entities.Items,System.String,System.Int64)">
            <summary>
            Tạo ItemsPriceListDto từ item
            </summary>
            <param name="item">Item cần chuyển đổi</param>
            <param name="priceListId">ID của price list</param>
            <param name="adjustedPrice">Giá đã được điều chỉnh</param>
            <returns>ItemsPriceListDto</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Implement.PriceListFlow.ApplyPaging(System.Collections.Generic.List{App.ECommerce.Repository.Entities.Items},App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Xử lý phân trang cho danh sách items
            </summary>
            <param name="items">Danh sách items cần phân trang</param>
            <param name="filter">Filter chứa thông tin phân trang</param>
            <returns>Danh sách items đã được phân trang</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IAffiliateFlow.ProcessCommissionPayments">
            <summary>
            Xử lý thanh toán hoa hồng cho tất cả shop đang hoạt động
            </summary>
            <returns></returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcUserPointByOrder(System.String)">
            <summary>
            Tích điểm user sau khi mua hàng
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcUserPointBySignUp(System.String)">
            <summary>
            Tính điểm user sau khi đăng ký
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcUserPointByShare(System.String)">
            <summary>
            Tính điểm user sau khi chia sẻ
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcUserPointsForReferralOrder(System.String)">
            <summary>
            Tính điểm user người được giới thiệu đặt hàng (A giới thiệu B, B đặt đơn hàng, orderId là id đơn của B)
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcUserLevel(System.String)">
            <summary>
            Tính level user sau khi đơn hàng hoàn thành
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcMaxPointCanUse(System.Int64,System.String)">
            <summary>
            Tính điểm tối đa có thể sử dụng cho đơn hàng
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.CalcPointGainAfterComplete(System.String)">
            <summary>
            Tính số điểm đạt được sau khi hoàn thành đơn
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ICalcMemberLevelFlow.RefundPointToUser(App.ECommerce.Repository.Entities.Order)">
            <summary>
            Hoàn điểm cho user khi hủy đơn
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.CalculatePriceItemWithPriceList(App.ECommerce.Repository.Entities.Items,System.String,System.String)">
            <summary>
            Tính giá sản phẩm theo PriceList
            </summary>
            <param name="items">Sản phẩm cần tính giá</param>
            <param name="userId">ID người dùng (để kiểm tra rank)</param>
            <param name="branchId">ID chi nhánh (để kiểm tra branch)</param>
            <returns>Sản phẩm với giá đã được tính theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.CalculatePriceWithPriceList(App.ECommerce.Resource.Dtos.ItemsGroupByDto,System.String,System.String,System.String)">
            <summary>
            Tính giá sản phẩm theo PriceList
            </summary>
            <param name="itemsGroupBy">Sản phẩm cần tính giá</param>
            <param name="userId">ID người dùng (để kiểm tra rank)</param>
            <param name="branchId">ID chi nhánh (để kiểm tra branch)</param>
            <returns>Sản phẩm với giá đã được tính theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.CalculateListItemWithPriceList(System.Collections.Generic.List{App.ECommerce.Resource.Dtos.ItemsGroupByDto},System.String,System.String)">
            <summary>
            Tính giá cho danh sách sản phẩm theo PriceList
            </summary>
            <param name="itemsGroupByList">Danh sách sản phẩm cần tính giá</param>
            <param name="userId">ID người dùng (để kiểm tra rank)</param>
            <param name="branchId">ID chi nhánh (để kiểm tra branch)</param>
            <returns>Danh sách sản phẩm với giá đã được tính theo PriceList</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.ValidateItemConflicts(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Kiểm tra sản phẩm đã tồn tại trong bảng giá khác chưa
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="itemIds">Danh sách ID sản phẩm/dịch vụ cần kiểm tra</param>
            <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
            <returns>Danh sách xung đột sản phẩm</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.ValidateRankConflicts(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Kiểm tra rank đã tồn tại trong bảng giá khác chưa
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="rankIds">Danh sách ID rank cần kiểm tra</param>
            <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
            <returns>Danh sách xung đột rank</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.ValidateItemQuantityTierConflicts(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Kiểm tra sản phẩm đã tồn tại trong bảng giá theo số lượng khác chưa
            </summary>
            <param name="shopId">ID của cửa hàng</param>
            <param name="itemIds">Danh sách ID sản phẩm/dịch vụ cần kiểm tra</param>
            <param name="excludePriceListId">ID bảng giá cần loại trừ (dùng cho update)</param>
            <returns>Danh sách xung đột sản phẩm/dịch vụ</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.GetItemsByPriceListId(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm theo priceListId, có phân trang và giá đã điều chỉnh
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách sản phẩm với giá đã điều chỉnh</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.GetItemsNotInAnyPriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm chưa thuộc bất kỳ bảng giá nào
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách sản phẩm chưa thuộc bảng giá nào</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.GetItemQuantityTiers(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách sản phẩm/dịch vụ theo bậc số lượng
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách sản phẩm/dịch vụ theo bậc số lượng</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.IPriceListFlow.GetRanksNotInAnyPriceList(App.ECommerce.Resource.Dtos.InputDtos.PriceListItemsFilterDto)">
            <summary>
            Lấy danh sách hạng thành viên chưa thuộc bất kỳ bảng giá nào
            </summary>
            <param name="filter">Thông tin lọc</param>
            <returns>Danh sách hạng thành viên chưa thuộc bảng giá nào</returns>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.SaveConfig(App.ECommerce.Resource.Dtos.SyncServiceConfigDto)">
            <summary>
            Lưu cấu hình đồng bộ
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.GetConfig(App.ECommerce.Units.Enums.SyncServiceEnum,System.String)">
            <summary>
            Lấy cấu hình theo shop ID và service
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.DeleteConfig(App.ECommerce.Units.Enums.SyncServiceEnum,System.String)">
            <summary>
            Xóa cấu hình
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.SyncProductFromWebhook(App.ECommerce.Units.Enums.SyncServiceEnum,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductWebhookDto,System.String)">
            <summary>
            Đồng bộ sản phẩm từ webhook
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.SyncOrderFromWebhook(App.ECommerce.Units.Enums.SyncServiceEnum,App.ECommerce.Resource.Dtos.Webhooks.NhanhOrderDataDto,System.String)">
            <summary>
            Đồng bộ đơn hàng từ webhook
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.SyncCustomerFromWebhook(App.ECommerce.Units.Enums.SyncServiceEnum,System.Object,System.String)">
            <summary>
            Đồng bộ khách hàng từ webhook
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.DeleteProductsFromWebhook(App.ECommerce.Units.Enums.SyncServiceEnum,System.Object)">
            <summary>
            Xóa sản phẩm từ webhook
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.DeleteOrderFromWebhook(App.ECommerce.Units.Enums.SyncServiceEnum,System.Object,System.String)">
            <summary>
            Xóa đơn hàng từ webhook
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.CreateOrderToExternalService(App.ECommerce.Units.Enums.SyncServiceEnum,App.ECommerce.Repository.Entities.Order,System.String)">
            <summary>
            Cập nhật đơn hàng lên external service
            </summary>
        </member>
        <member name="M:App.ECommerce.ProcessFlow.Interface.ISyncServiceFlow.UpdateAccessCode(App.ECommerce.Resource.Dtos.UpdateAccessCodeDto)">
            <summary>
            Cập nhật access code
            </summary>
        </member>
        <member name="P:App.ECommerce.Repository.Entities.SyncServiceConfig.AdditionalConfig">
            <summary>
            Cấu hình bổ sung dạng JSON cho từng service cụ thể
            </summary>
        </member>
        <member name="P:App.ECommerce.Repository.Entities.Voucher.RemainingStock">
            <summary>
            Số lượng voucher còn lại (không lưu DB, tính toán runtime)
            Quantity - số voucher đã phát cho user (bao gồm cả chưa sử dụng)
            </summary>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.UserGroupRepository.CalculateUserCurrentPoint(App.ECommerce.Repository.Entities.User)">
            <summary>
            Tính toán điểm hiện tại của user dựa trên logic từ CalculateUserScoreDetails
            </summary>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetAuthorizationURL(System.String,App.ECommerce.Repository.Entities.ShopSetting)">
            <summary>
            Tạo URL ủy quyền cho Zalo OA
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <returns>Đối tượng Zalo_Authorization_History chứa thông tin ủy quyền</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.CallBack(System.String,App.ECommerce.Repository.Entities.Zalo_Authorization_History)">
            <summary>
            Xử lý callback từ Zalo OA
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objHistory">Đối tượng Zalo_Authorization_History chứa thông tin ủy quyền</param>
            <returns>Đối tượng Shop chứa thông tin OA</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetAccessToken(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Lấy access token từ Zalo API
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="grantType">Loại grant type (authorization_code hoặc refresh_token)</param>
            <param name="parameters">Các tham số cần thiết cho việc lấy token</param>
            <returns>Đối tượng Zalo_Token chứa thông tin token</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetAccessTokenByAuthorizationCode(System.String,System.String,System.String)">
            <summary>
            Lấy access token bằng authorization code
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="authorizationCode">Mã authorization code</param>
            <param name="codeVerifier">Mã code verifier</param>
            <returns>Đối tượng Zalo_Token chứa thông tin token</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetAccessTokenByRefreshToken(System.String,System.String)">
            <summary>
            Lấy access token mới bằng refresh token
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="refreshToken">Refresh token hiện tại</param>
            <returns>Đối tượng Zalo_Token chứa thông tin token mới</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetUserMessageQuota(System.String,App.ECommerce.Repository.Entities.ShopSetting,System.String)">
            <summary>
            Kiểm tra hạn mức gửi tin tới User cụ thể
            Kiểm tra mức sử dụng tin nhắn Tư vấn miễn phí trong khung 48h
            Kiểm tra hạn mức gửi tin nhắn Truyền thông theo ngày/tháng
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="userId">ID của user cần kiểm tra</param>
            <returns>Thông tin quota tin nhắn</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.SendZaloMessage(System.String,App.ECommerce.Repository.Entities.ShopSetting,Newtonsoft.Json.Linq.JObject,System.String)">
            <summary>
            Gửi tin nhắn qua Zalo API
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="objMessage">Nội dung tin nhắn cần gửi</param>
            <param name="messageType">Loại tin nhắn (transaction/promotion/cs)</param>
            <returns>ID của tin nhắn đã gửi</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.SendTransactionMessage(System.String,App.ECommerce.Repository.Entities.ShopSetting,App.ECommerce.Repository.Entities.Zalo_Transaction)">
            <summary>
            Gửi tin nhắn giao dịch qua Zalo
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="objSend">Nội dung tin nhắn cần gửi</param>
            <returns>ID của tin nhắn đã gửi</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.SendPromotionMessage(System.String,App.ECommerce.Repository.Entities.ShopSetting,App.ECommerce.Repository.Entities.Zalo_Transaction)">
            <summary>
            Gửi tin nhắn truyền thông qua Zalo
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="objSend">Nội dung tin nhắn cần gửi</param>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.SendConsultation(System.String,App.ECommerce.Repository.Entities.ShopSetting,App.ECommerce.Repository.Entities.Zalo_Transaction)">
            <summary>
            Gửi tin nhắn tư vấn qua Zalo
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="objSend">Nội dung tin nhắn cần gửi</param>
            <returns>ID của tin nhắn đã gửi</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetOAZNSMessageQuota(System.String,App.ECommerce.Repository.Entities.ShopSetting)">
            <summary>
            Kiểm tra hạn mức gửi tin ZNS của OA
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <returns>Thông tin quota ZNS</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetListTemplateZNS(System.String,App.ECommerce.Repository.Entities.ShopSetting,System.Int32)">
            <summary>
            Lấy danh sách template ZNS
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="status">Trạng thái của template</param>
            <returns>Thông tin quota ZNS</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetDetailTemplateZNSByTemplateId(System.String,App.ECommerce.Repository.Entities.ShopSetting,System.String)">
            <summary>
            Lấy thông tin chi tiết template ZNS
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="templateId">ID mẫu tin ZNS</param>
            <returns>Chi </returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetTemplateZNSSampleData(System.String,App.ECommerce.Repository.Entities.ShopSetting,System.String)">
            <summary>
            Lấy dữ liệu mẫu của template ZNS
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="templateId">ID mẫu tin ZNS</param>
            <returns>Dữ liệu mẫu của template</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.SendZaloZNS(System.String,System.String,Newtonsoft.Json.Linq.JObject)">
            <summary>
            Gửi ZNS qua Zalo API
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="accessToken">AccessToken của OA gửi ZNS</param>
            <param name="objMessage">Nội dung tin nhắn cần gửi</param>
            <returns>ID của tin nhắn đã gửi</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetZNSStatusInfoByMessageId(System.String,App.ECommerce.Repository.Entities.ShopSetting,System.String)">
            <summary>
            Lấy thông tin trạng thái ZNS
            </summary>
            <param name="requestId">ID của yêu cầu</param>
            <param name="objOA">Đối tượng Partner OA</param>
            <param name="message_id">ID tin ZNS đã gửi</param>
            <returns>Trạng thái tin ZNS đã gửi</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Implement.ZaloRepository.GetCustomerReviews(App.ECommerce.Resource.Dtos.ZaloZNSRatingRequestDto)">
            <summary>
            Lấy thông tin đánh giá Template từ Zalo
            </summary>
            <param name="objRequest">Các thông tin tìm kiếm sang Zalo</param>
            <returns>Danh sách đánh giá từ Zalo</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.Create(App.ECommerce.Repository.Entities.TransportMethod)">
            <summary>
            Tạo một phương thức vận chuyển mới
            </summary>
            <param name="transportMethod">Đối tượng phương thức vận chuyển</param>
            <returns>Phương thức vận chuyển đã tạo</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.Update(App.ECommerce.Repository.Entities.TransportMethod)">
            <summary>
            Cập nhật phương thức vận chuyển
            </summary>
            <param name="transportMethod">Đối tượng phương thức vận chuyển cần cập nhật</param>
            <returns>Phương thức vận chuyển đã cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.Delete(System.String)">
            <summary>
            Xóa phương thức vận chuyển theo ID
            </summary>
            <param name="transportMethodId">ID phương thức vận chuyển</param>
            <returns>True nếu xóa thành công</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.FindByTransportMethodId(System.String)">
            <summary>
            Tìm phương thức vận chuyển theo ID
            </summary>
            <param name="transportMethodId">ID phương thức vận chuyển</param>
            <returns>Đối tượng phương thức vận chuyển</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.GetByShopId(System.String)">
            <summary>
            Lấy danh sách tất cả phương thức vận chuyển theo ShopId
            </summary>
            <param name="shopId">ID của Shop</param>
            <returns>Danh sách phương thức vận chuyển</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.GetList(App.ECommerce.Resource.Model.Paging,System.String,System.Nullable{App.ECommerce.Units.Enums.TransportCodeType})">
            <summary>
            Lấy danh sách phương thức vận chuyển có phân trang
            </summary>
            <param name="paging">Thông tin phân trang</param>
            <param name="shopId">ID của Shop (tùy chọn)</param>
            <param name="transportCode">Loại vận chuyển (tùy chọn)</param>
            <returns>Kết quả phân trang với danh sách phương thức vận chuyển</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.FindByTransportCode(App.ECommerce.Units.Enums.TransportCodeType,System.String)">
            <summary>
            Tìm phương thức vận chuyển theo loại mã vận chuyển và ShopId
            </summary>
            <param name="transportCode">Loại mã vận chuyển</param>
            <param name="shopId">ID của Shop</param>
            <returns>Phương thức vận chuyển</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.ToggleStatus(System.String,System.Boolean)">
            <summary>
            Bật hoặc tắt một phương thức vận chuyển
            </summary>
            <param name="transportMethodId">ID phương thức vận chuyển</param>
            <param name="isEnabled">Trạng thái kích hoạt</param>
            <returns>Phương thức vận chuyển đã cập nhật</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.GetEnabledByShopId(System.String)">
            <summary>
            Lấy danh sách các phương thức vận chuyển được kích hoạt theo ShopId
            </summary>
            <param name="shopId">ID của Shop</param>
            <returns>Danh sách phương thức vận chuyển được kích hoạt</returns>
        </member>
        <member name="M:App.ECommerce.Repository.Interface.ITransportMethodRepository.UpdateConfig(System.String,App.ECommerce.Repository.Entities.TransportConfig)">
            <summary>
            Cập nhật cấu hình cho phương thức vận chuyển
            </summary>
            <param name="transportMethodId">ID phương thức vận chuyển</param>
            <param name="config">Đối tượng cấu hình</param>
            <returns>Phương thức vận chuyển đã cập nhật</returns>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1">
            <summary>
            Generic response wrapper cho tất cả API responses từ Nhanh.vn
            </summary>
            <typeparam name="T">Kiểu dữ liệu của data response</typeparam>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1.Code">
            <summary>
            Mã trạng thái API (1 = thành công, khác 1 = lỗi)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1.Messages">
            <summary>
            Thông báo lỗi nếu có
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1.Data">
            <summary>
            Dữ liệu response
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1.IsSuccess">
            <summary>
            Kiểm tra xem API call có thành công không
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhApiResponse`1.FirstErrorMessage">
            <summary>
            Lấy thông báo lỗi đầu tiên nếu có
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCustomerSearchData">
            <summary>
            Response data cho API customer search
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCustomerSearchData.Customers">
            <summary>
            Danh sách khách hàng (dạng dictionary với key là customer ID)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCustomerSearchData.TotalPages">
            <summary>
            Tổng số trang
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCustomerSearchData.Page">
            <summary>
            Trang hiện tại
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCategoryData">
            <summary>
            Response data cho API category
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCategoryData.Categories">
            <summary>
            Danh sách categories
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductData">
            <summary>
            Response data cho API product
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductData.Products">
            <summary>
            Danh sách sản phẩm
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductData.Total">
            <summary>
            Tổng số sản phẩm
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto">
            <summary>
            Response data cho API create order của Nhanh.vn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.OrderId">
            <summary>
            ID đơn hàng trên Nhanh.vn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.ShipFee">
            <summary>
            Phí vận chuyển (chỉ có khi autoSend=1)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.CodFee">
            <summary>
            Phí thu tiền hộ (chỉ có khi autoSend=1)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.DeclaredFee">
            <summary>
            Phí bảo hiểm (chỉ có khi autoSend=1)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.CarrierCode">
            <summary>
            Mã vận đơn (chỉ có khi autoSend=1)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhCreateOrderResponseDto.TrackingUrl">
            <summary>
            Lịch trình đơn hàng (chỉ có khi autoSend=1)
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderSearchResponse">
            <summary>
            Response data cho API order search từ Nhanh.vn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderSearchResponse.Page">
            <summary>
            Trang hiện tại
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderSearchResponse.TotalPages">
            <summary>
            Tổng số trang
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderSearchResponse.TotalRecords">
            <summary>
            Tổng số records
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderSearchResponse.Orders">
            <summary>
            Dictionary chứa danh sách đơn hàng với key là ID đơn hàng
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderDetailDto">
            <summary>
            Chi tiết đơn hàng từ API search của Nhanh.vn
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderProductDetailDto">
            <summary>
            Chi tiết sản phẩm trong đơn hàng từ Nhanh.vn
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductSearchResponse">
            <summary>
            Response data cho API product search từ Nhanh.vn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductSearchResponse.CurrentPage">
            <summary>
            Trang hiện tại
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductSearchResponse.TotalPages">
            <summary>
            Tổng số trang
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductSearchResponse.Products">
            <summary>
            Dictionary chứa danh sách sản phẩm với key là ID sản phẩm
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto">
            <summary>
            Chi tiết sản phẩm từ API search của Nhanh.vn
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductInventoryDetailDto">
            <summary>
            Chi tiết inventory từ API search
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductAttributeDetailDto">
            <summary>
            Chi tiết attribute từ API search
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto">
            <summary>
            DTO cho việc đồng bộ khách hàng từ Nhanh.vn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Id">
            <summary>
            ID khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Type">
            <summary>
            Loại khách hàng (1: cá nhân, 2: doanh nghiệp)
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Name">
            <summary>
            Tên khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Mobile">
            <summary>
            Số điện thoại
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Gender">
            <summary>
            Giới tính
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Email">
            <summary>
            Email
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Address">
            <summary>
            Địa chỉ
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Birthday">
            <summary>
            Ngày sinh
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Code">
            <summary>
            Mã khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Level">
            <summary>
            Cấp độ khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Group">
            <summary>
            Nhóm khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.LevelId">
            <summary>
            ID cấp độ khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.GroupId">
            <summary>
            ID nhóm khách hàng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.CityLocationId">
            <summary>
            ID vị trí thành phố
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.DistrictLocationId">
            <summary>
            ID vị trí quận/huyện
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.WardLocationId">
            <summary>
            ID vị trí phường/xã
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.TotalMoney">
            <summary>
            Tổng tiền đã mua
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.StartedDate">
            <summary>
            Ngày bắt đầu
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.StartedDepotId">
            <summary>
            ID kho bắt đầu
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Points">
            <summary>
            Điểm tích lũy
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.TotalBills">
            <summary>
            Tổng số hóa đơn
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.LastBoughtDate">
            <summary>
            Ngày mua cuối cùng
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.TaxCode">
            <summary>
            Mã số thuế
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.BusinessName">
            <summary>
            Tên doanh nghiệp
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.BusinessAddress">
            <summary>
            Địa chỉ doanh nghiệp
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.NhanhDtos.NhanhSyncCustomerDto.Description">
            <summary>
            Mô tả
            </summary>
        </member>
        <member name="T:App.ECommerce.Resource.Dtos.VoucherDtos.RedeemVoucherDto">
            <summary>
            DTO chứa kết quả thu thập voucher
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.VoucherDtos.RedeemVoucherDto.Success">
            <summary>
            Trạng thái thành công hay thất bại
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.VoucherDtos.RedeemVoucherDto.Message">
            <summary>
            Thông báo kết quả
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.VoucherDtos.RedeemVoucherDto.VoucherCode">
            <summary>
            Mã voucher đã thu thập
            </summary>
        </member>
        <member name="P:App.ECommerce.Resource.Dtos.VoucherDtos.RedeemVoucherDto.ExchangePoints">
            <summary>
            Số điểm cần đổi (nếu voucher yêu cầu đổi điểm)
            </summary>
        </member>
        <member name="T:App.ECommerce.Services.Jobs.Job.SyncNhanhOrdersJob">
            <summary>
            Job đồng bộ đơn hàng từ Nhanh.vn
            </summary>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhOrdersJob.SyncOrdersForShop(System.String,System.String)">
            <summary>
            Đồng bộ đơn hàng cho một shop cụ thể
            </summary>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhOrdersJob.ProcessSingleOrder(System.String,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderDetailDto,System.String)">
            <summary>
            Xử lý một đơn hàng cụ thể
            </summary>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhOrdersJob.MapToOrderDataDto(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhOrderDetailDto)">
            <summary>
            Chuyển đổi từ NhanhOrderDetailDto sang NhanhOrderDataDto
            </summary>
        </member>
        <member name="T:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob">
            <summary>
            Job đồng bộ sản phẩm từ Nhanh.vn mỗi ngày
            </summary>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.SyncProductsForShop(System.String,System.String)">
            <summary>
            Đồng bộ sản phẩm cho một shop cụ thể
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="requestId">ID request để tracking log</param>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.ExecuteWithRetry``1(System.Func{System.Threading.Tasks.Task{``0}},System.String,System.String)">
            <summary>
            Thực hiện API call với exponential backoff retry khi gặp lỗi
            </summary>
            <typeparam name="T">Kiểu dữ liệu trả về</typeparam>
            <param name="apiCall">Function thực hiện API call</param>
            <param name="requestId">ID request để tracking log</param>
            <param name="operationName">Tên operation để log</param>
            <returns>Kết quả API call</returns>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.IsRateLimitError(System.Exception)">
            <summary>
            Kiểm tra xem lỗi có phải là rate limiting không
            </summary>
            <param name="exception">Exception cần kiểm tra</param>
            <returns>True nếu là rate limiting error</returns>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.SyncSingleProduct(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto,System.String,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductCategoryDto},System.String)">
            <summary>
            Đồng bộ một sản phẩm cụ thể
            </summary>
            <param name="nhanhProduct">Sản phẩm từ Nhanh.vn</param>
            <param name="shopId">ID của shop</param>
            <param name="categories">Danh sách categories đã lấy từ Nhanh.vn</param>
            <param name="requestId">ID request để tracking log</param>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.GetAllParentProducts(System.String,System.String)">
            <summary>
            Lấy tất cả parent products (sản phẩm cha có biến thể) từ Nhanh.vn
            </summary>
            <param name="shopId">ID của shop</param>
            <param name="requestId">ID request để tracking log</param>
            <returns>Dictionary với key là parentId và value là parent product</returns>
        </member>
        <member name="M:App.ECommerce.Services.Jobs.Job.SyncNhanhProductsJob.SyncSingleProductWithParentCache(App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto,System.String,System.Collections.Generic.List{App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductCategoryDto},System.Collections.Generic.Dictionary{System.Int32,App.ECommerce.Resource.Dtos.NhanhDtos.NhanhProductDetailDto},System.String)">
            <summary>
            Đồng bộ một sản phẩm cụ thể với parent products cache
            </summary>
            <param name="nhanhProduct">Sản phẩm từ Nhanh.vn</param>
            <param name="shopId">ID của shop</param>
            <param name="categories">Danh sách categories</param>
            <param name="parentProductsCache">Cache của parent products</param>
            <param name="requestId">ID request để tracking log</param>
        </member>
        <member name="M:App.ECommerce.Units.Common.FormatPhoneNumberVN(System.String)">
            <summary>
            Tự động chuẩn hóa số điện thoại di động Việt Nam về định dạng chuẩn
            </summary>
            <param name="phoneNumber">Số điện thoại cần chuẩn hóa</param>
            <returns>Số điện thoại đã được chuẩn hóa ở định dạng +84, trả về null nếu không hợp lệ</returns>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_SUCCESS">
            <summary>
            Xác nhận đặt hàng thành công
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_SUCCESS">
            <summary>
            Xác nhận thanh toán thành công
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_CANCELED">
            <summary>
            Thông báo đơn hàng bị hủy
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_REFUND">
            <summary>
            Thông báo hoàn tiền
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_ELECTRONIC_INVOICE">
            <summary>
            Thông báo hóa đơn điện tử
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY">
            <summary>
            Thông báo giao hàng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_DELIVERY_SUCCESS">
            <summary>
            Thông báo giao hàng thành công
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_SERVICE_REGISTRATION">
            <summary>
            Thông báo đăng ký dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_SERVICE_RENEWAL">
            <summary>
            Thông báo gia hạn dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_PAYMENT_FAILED">
            <summary>
            Thông báo thanh toán thất bại
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.TRANSACTION_NOTIFICATION_ORDER_PROCESSING">
            <summary>
            Thông báo đơn hàng đang xử lý
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_PAYMENT_REMINDER">
            <summary>
            Nhắc nhở hoàn tất thanh toán
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_POST_ORDER_FOLLOW_UP">
            <summary>
            Hỏi thăm sau khi đặt hàng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_APPOINTMENT_REMINDER">
            <summary>
            Nhắc nhở lịch hẹn
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_BIRTHDAY_GREETING">
            <summary>
            Chúc mừng sinh nhật
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_FOLLOW_UP_REMINDER">
            <summary>
            Nhắc nhở tái khám
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_COURSE_COMPLETION_REMINDER">
            <summary>
            Nhắc nhở hoàn thành khóa học
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_POST_SERVICE_FOLLOW_UP">
            <summary>
            Hỏi thăm sau dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_SERVICE_RENEWAL_REMINDER">
            <summary>
            Nhắc nhở gia hạn dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_SYSTEM_MAINTENANCE_NOTICE">
            <summary>
            Thông báo bảo trì hệ thống
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_VIP_SUPPORT">
            <summary>
            Chăm sóc khách hàng VIP
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_CREATE_MINIAPP_ACCOUNT">
            <summary>
            Khách hàng tạo tài khoản Mini App
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_FOLLOW_OA">
            <summary>
            Khách hàng quan tâm OA
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.CUSTOMER_CARE_UNFOLLOW_OA">
            <summary>
            Khách hàng hủy quan tâm OA
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_PRODUCT_SERVICE_REVIEW_REQUEST">
            <summary>
            Yêu cầu đánh giá sản phẩm/dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_POST_PURCHASE_PROMOTION">
            <summary>
            Khuyến mãi sau mua hàng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_WARRANTY_REPAIR_REMINDER">
            <summary>
            Nhắc nhở bảo hành/sửa chữa
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_PERIODIC_MAINTENANCE_REMINDER">
            <summary>
            Nhắc nhở bảo dưỡng định kỳ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_SERVICE_COMPLETION_NOTICE">
            <summary>
            Thông báo hoàn thành dịch vụ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_REFERRAL_ENCOURAGEMENT">
            <summary>
            Khuyến khích giới thiệu bạn bè
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_PERIODIC_SERVICE_REMINDER">
            <summary>
            Nhắc nhở sử dụng dịch vụ định kỳ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_COMPLETION_CERTIFICATE_NOTICE">
            <summary>
            Thông báo chứng chỉ hoàn thành
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_EVENT_THANK_YOU">
            <summary>
            Cảm ơn sau sự kiện
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_PROMOTION_NOTICE">
            <summary>
            Thông báo khuyến mãi
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_EVENT_INVITATION">
            <summary>
            Mời tham gia sự kiện
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_NEW_PRODUCT_SERVICE_INTRODUCTION">
            <summary>
            Giới thiệu sản phẩm/dịch vụ mới
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_POLICY_CHANGE_NOTICE">
            <summary>
            Thông báo thay đổi chính sách
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_BRANCH_OPENING_NOTICE">
            <summary>
            Thông báo khai trương chi nhánh
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_HOLIDAY_NOTICE">
            <summary>
            Thông báo nghỉ lễ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_LOYALTY_PROGRAM">
            <summary>
            Chương trình khách hàng thân thiết
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_WEBINAR_SEMINAR_NOTICE">
            <summary>
            Thông báo hội thảo/webinar
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_APP_UPDATE_NOTICE">
            <summary>
            Thông báo cập nhật ứng dụng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.AFTER_SALES_HOLIDAY_GREETING">
            <summary>
            Chúc mừng ngày lễ
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_ORDER_PROCESSING">
            <summary>
            Thông báo đơn hàng cần xử lý
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_PAYMENT">
            <summary>
            Thông báo thanh toán cho đối tác
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_DELIVERY_STATUS_UPDATE">
            <summary>
            Yêu cầu cập nhật trạng thái giao hàng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_NEW_COLLABORATION">
            <summary>
            Thông báo hợp tác mới
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_OUT_OF_STOCK">
            <summary>
            Thông báo hết hàng trong kho
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_SALES_REPORT_REQUEST">
            <summary>
            Yêu cầu báo cáo doanh số
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_PARTNER_EVENT">
            <summary>
            Thông báo sự kiện đối tác
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_CONTRACT_CHANGE">
            <summary>
            Thông báo thay đổi hợp đồng
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_INVOICE_PAYMENT_REMINDER">
            <summary>
            Nhắc nhở thanh toán hóa đơn
            </summary>
        </member>
        <member name="F:App.ECommerce.Units.Consts.TriggerEventConst.PARTNER_NOTIFICATION_PERFORMANCE_REVIEW">
            <summary>
            Thông báo đánh giá hiệu suất
            </summary>
        </member>
        <member name="M:App.ECommerce.Units.ImageEx.ResizeToWebpAsync(System.IO.Stream,System.Int32,System.Int32,System.Int32,SixLabors.ImageSharp.Formats.Webp.WebpEncodingMethod)">
            <summary>
            Resize và chuyển đổi ảnh sang định dạng WebP từ Stream. Tự động nhận diện HEIC và chuyển sang JPEG nếu cần.
            </summary>
            <param name="inputStream">Stream chứa dữ liệu ảnh gốc</param>
            <param name="width">Chiều rộng tối đa</param>
            <param name="height">Chiều cao tối đa</param>
            <param name="quality">Chất lượng ảnh WebP (0-100)</param>
            <param name="encodingMethod">Phương pháp encode WebP</param>
            <returns>Ảnh WebP dạng byte[]</returns>
        </member>
        <member name="M:App.ECommerce.Units.ImageEx.ResizeToWebpAsync(System.Byte[],System.Int32,System.Int32,System.Int32,SixLabors.ImageSharp.Formats.Webp.WebpEncodingMethod)">
            <summary>
            Resize và chuyển đổi ảnh sang WebP từ mảng byte[]. Tự động nhận diện HEIC và chuyển sang JPEG nếu cần.
            </summary>
            <param name="imageBytes">Dữ liệu ảnh gốc dạng byte[]</param>
            <param name="width">Chiều rộng tối đa</param>
            <param name="height">Chiều cao tối đa</param>
            <param name="quality">Chất lượng ảnh WebP (0-100)</param>
            <param name="encodingMethod">Phương pháp encode WebP</param>
            <returns>Ảnh WebP dạng byte[]</returns>
        </member>
        <member name="M:App.ECommerce.Units.ImageEx.ResizeToWebpAsync(System.String,System.Int32,System.Int32,System.Int32,SixLabors.ImageSharp.Formats.Webp.WebpEncodingMethod)">
            <summary>
            Resize và chuyển đổi ảnh sang WebP từ file path. Tự động nhận diện HEIC và chuyển sang JPEG nếu cần.
            </summary>
            <param name="filePath">Đường dẫn file ảnh gốc</param>
            <param name="width">Chiều rộng tối đa</param>
            <param name="height">Chiều cao tối đa</param>
            <param name="quality">Chất lượng ảnh WebP (0-100)</param>
            <param name="encodingMethod">Phương pháp encode WebP</param>
            <returns>Ảnh WebP dạng byte[]</returns>
        </member>
        <member name="M:App.ECommerce.Units.ImageEx.GetImageFormat(System.Byte[])">
            <summary>
            Kiểm tra định dạng ảnh dựa vào header hoặc extension. Hỗ trợ HEIC, JPEG, PNG, ...
            </summary>
            <param name="imageBytes">Dữ liệu ảnh dạng byte[]</param>
            <returns>Tên định dạng ảnh (viết hoa), hoặc "UNKNOWN" nếu không xác định được</returns>
        </member>
        <member name="M:App.ECommerce.Units.ImageEx.ConvertHeicToJpeg(System.Byte[])">
            <summary>
            Chuyển đổi ảnh HEIC sang JPEG bằng Magick.NET.
            </summary>
            <param name="heicBytes">Dữ liệu ảnh HEIC dạng byte[]</param>
            <returns>Ảnh JPEG dạng byte[]</returns>
        </member>
        <member name="M:Zalo_Button_Repository.ListButtonAction(System.Guid)">
            <summary>
            Retrieves a list of all Zalo button actions from the database.
            </summary>
            <param name="requestId">The unique request identifier for logging and tracking purposes.</param>
            <returns>A task that represents the asynchronous operation, containing a list of Zalo button actions.</returns>
        </member>
        <member name="M:IZalo_Button_Repository.ListButtonAction(System.Guid)">
            <summary>
            Retrieves a list of all Zalo button actions from the database.
            </summary>
            <param name="requestId">The unique request identifier for logging and tracking purposes.</param>
            <returns>A task that represents the asynchronous operation, containing a list of Zalo button actions.</returns>
        </member>
        <member name="M:IZalo_Message_Type_Repository.CreateMessageType(App.ECommerce.Repository.Entities.Zalo_MessageType,System.Guid)">
            <summary>
            Creates a new Zalo message type.
            </summary>
            <param name="item">The Zalo message type to create.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The created Zalo message type.</returns>
        </member>
        <member name="M:IZalo_Message_Type_Repository.ListMessageTypes(System.Guid)">
            <summary>
            Retrieves a paginated list of Zalo message types based on filtering criteria.
            </summary>
            <param name="requestId">The unique request identifier.</param>
            <returns>A paginated result containing the list of Zalo message types.</returns>
        </member>
        <member name="M:IZalo_Message_Type_Repository.GetMessageTypeById(System.Guid,System.Guid)">
            <summary>
            Retrieves a Zalo message type by its ID.
            </summary>
            <param name="id">The ID of the message type to retrieve.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The Zalo message type if found, or null if not found.</returns>
        </member>
        <member name="M:IZalo_Message_Type_Repository.UpdateMessageType(App.ECommerce.Repository.Entities.Zalo_MessageType,System.Guid)">
            <summary>
            Updates an existing Zalo message type.
            </summary>
            <param name="item">The updated Zalo message type object.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The updated Zalo message type if successful, or null if not found.</returns>
        </member>
        <member name="M:IZalo_Message_Type_Repository.DeleteMessageType(System.Guid,System.Guid)">
            <summary>
            Deletes a Zalo message type by its ID.
            </summary>
            <param name="id">The ID of the message type to delete.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>True if the message type was deleted, false if not found.</returns>
        </member>
        <member name="M:IZalo_Send_Type_Repository.CreateSendType(App.ECommerce.Repository.Entities.Zalo_SendType,System.Guid)">
            <summary>
            Creates a new Zalo send type.
            </summary>
            <param name="item">The Zalo send type to create.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The created Zalo send type.</returns>
        </member>
        <member name="M:IZalo_Send_Type_Repository.ListSendTypes(System.Guid)">
            <summary>
            Retrieves a list of Zalo send types.
            </summary>
            <param name="requestId">The unique request identifier.</param>
            <returns>A list of Zalo send types.</returns>
        </member>
        <member name="M:IZalo_Send_Type_Repository.GetSendTypeById(System.Guid,System.Guid)">
            <summary>
            Retrieves a Zalo send type by its ID.
            </summary>
            <param name="id">The ID of the send type to retrieve.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The Zalo send type if found, or null if not found.</returns>
        </member>
        <member name="M:IZalo_Send_Type_Repository.UpdateSendType(App.ECommerce.Repository.Entities.Zalo_SendType,System.Guid)">
            <summary>
            Updates an existing Zalo send type.
            </summary>
            <param name="item">The updated Zalo send type object.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>The updated Zalo send type if successful, or null if not found.</returns>
        </member>
        <member name="M:IZalo_Send_Type_Repository.DeleteSendType(System.Guid,System.Guid)">
            <summary>
            Deletes a Zalo send type by its ID.
            </summary>
            <param name="id">The ID of the send type to delete.</param>
            <param name="requestId">The unique request identifier.</param>
            <returns>True if the send type was deleted, false if not found.</returns>
        </member>
        <member name="M:BackgroundQueue.BackgroundTaskQueue.Enqueue(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.BackgroundTaskQueue.Enqueue(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Action{System.Exception})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.BackgroundTaskQueue.Enqueue(BackgroundQueue.Models.Ticket)">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.BackgroundTaskQueue.DequeueAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Action{System.Exception})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync(BackgroundQueue.Generic.Models.Ticket)">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}},System.Action{System.Exception})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.ProcessInQueueAsync``1(BackgroundQueue.Generic.Models.Ticket{``0})">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.BackgroundResultQueue.DequeueAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return when the task got processed.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Action{System.Exception})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return when the task got processed.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
            <param name="exception">A action which will get called, if the task fails.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync(BackgroundQueue.Generic.Models.Ticket)">
            <summary>
            Adds a new <see cref="T:BackgroundQueue.Generic.Models.Ticket"/> to the Queue, which will get processed in a background thread. This method will return when the ticket got processed.
            </summary>
            <param name="ticket">The Ticket which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return when the task got processed.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync``1(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{``0}},System.Action{System.Exception})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return when the task got processed.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
            <param name="exception">A action which will get called, if the task fails.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.ProcessInQueueAsync``1(BackgroundQueue.Generic.Models.Ticket{``0})">
            <summary>
            Adds a new <see cref="T:BackgroundQueue.Generic.Models.Ticket"/> to the Queue, which will get processed in a background thread. This method will return when the ticket got processed.
            </summary>
            <param name="ticket">The Ticket which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.Generic.IBackgroundResultQueue.DequeueAsync(System.Threading.CancellationToken)">
            <summary>
            Dequeues a <see cref="T:BackgroundQueue.Generic.Models.TicketBase"/> from the <see cref="T:BackgroundQueue.IBackgroundTaskQueue"/>.
            </summary>
            <returns>Returns the enqueued <see cref="T:BackgroundQueue.Generic.Models.TicketBase"/>.</returns>
        </member>
        <member name="T:BackgroundQueue.Generic.Models.Ticket">
            <summary>
            Inherit from this class, if you want to create a new Ticket, which should get enqueued in a BackgroundResultQueue.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Generic.Models.Ticket.ExecuteAsync(System.Threading.CancellationToken)">
            <summary>
            Contains the core logic of the Ticket.
            </summary>
        </member>
        <member name="T:BackgroundQueue.Generic.Models.Ticket`1">
            <summary>
            Inherit from this class, if you want to create a new Ticket, which should get enqueued in a BackgroundResultQueue.
            </summary>
        </member>
        <member name="T:BackgroundQueue.Generic.Models.TicketBase">
            <summary>
            This class is only for internal use.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Generic.Models.TicketBase.Enqueued">
            <summary>
            Gets called when the TicketBase gets enqueued.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Generic.Models.TicketBase.OnException(System.Exception)">
            <summary>
            Gets called when the ExecuteAsync method errors out.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Generic.ServiceCollectionExtensions.AddBackgroundResultQueue(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the required BackgroundResultQueue services.
            BackgroundResultQueue: là một hàng đợi sẽ liệt kê các mục và đợi cho đến khi Task/Ticket xử lý xong bao gồm cả kết quả.
            Bạn có thể sử dụng hàng đợi này nếu bạn muốn các yêu cầu được xử lý từng bước nhưng vẫn muốn mọi thứ diễn ra không đồng bộ.
            </summary>
        </member>
        <member name="M:BackgroundQueue.IBackgroundTaskQueue.Enqueue(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return immediately.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.IBackgroundTaskQueue.Enqueue(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Action{System.Exception})">
            <summary>
            Adds a new <see cref="T:System.Threading.Tasks.Task"/> to the Queue, which will get processed in a background thread. This method will return immediately.
            </summary>
            <param name="task">The Task which will get enqueued.</param>
            <param name="exception">A action which will get called, if the task fails.</param>
        </member>
        <member name="M:BackgroundQueue.IBackgroundTaskQueue.Enqueue(BackgroundQueue.Models.Ticket)">
            <summary>
            Adds a new <see cref="T:BackgroundQueue.Models.Ticket"/> to the Queue, which will get processed in a background thread. This method will return immediately.
            </summary>
            <param name="ticket">The ticket which will get enqueued.</param>
        </member>
        <member name="M:BackgroundQueue.IBackgroundTaskQueue.DequeueAsync(System.Threading.CancellationToken)">
            <summary>
            Dequeues a <see cref="T:BackgroundQueue.Models.Ticket"/> from the <see cref="T:BackgroundQueue.IBackgroundTaskQueue"/>.
            </summary>
            <returns>Returns the enqueued <see cref="T:BackgroundQueue.Models.Ticket"/>.</returns>
        </member>
        <member name="T:BackgroundQueue.Models.Ticket">
            <summary>
            Inherit from this class, if you want to create a new Ticket, which should get enqueued in a BackgroundTaskQueue.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Models.Ticket.Enqueued">
            <summary>
            Gets called when the <see cref="T:BackgroundQueue.Models.BaseTicket"/> gets enqueued.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Models.Ticket.OnException(System.Exception)">
            <summary>
            Gets called when the <see cref="M:BackgroundQueue.Models.Ticket.ExecuteAsync(System.Threading.CancellationToken)"/> method errors out.
            </summary>
        </member>
        <member name="M:BackgroundQueue.Models.Ticket.ExecuteAsync(System.Threading.CancellationToken)">
            <summary>
            Contains the core logic of the Ticket.
            </summary>
        </member>
        <member name="M:BackgroundQueue.ServiceCollectionExtensions.AddBackgroundTaskQueue(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the required BackgroundTaskQueue services.
            BackgroundTaskQueue: là một hàng đợi sẽ liệt kê các mục và ngay lập tức quay lại quá trình thực hiện hiện tại.
            Bạn có thể sử dụng hàng đợi này để gửi email.
            </summary>
        </member>
    </members>
</doc>
