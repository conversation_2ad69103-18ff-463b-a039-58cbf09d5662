using App.Base.Repository.Entities;
using App.Base.Utilities;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using AutoMapper;

using MongoDB.Bson;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace App.ECommerce.Helpers;

public class NhanhHelper : INhanhHelper
{
    protected readonly ICryptoRepository _cryptoRepository;
    protected readonly ISyncServiceConfigRepository _syncConfigRepository;
    protected readonly ISyncServiceHelper _syncServiceHelper;
    protected readonly IMapper _mapper;
    protected readonly IItemsRepository _itemsRepository;
    protected readonly IOrderRepository _orderRepository;
    protected readonly ICategoryRepository _categoryRepository;
    protected readonly IShopRepository _shopRepository;
    protected readonly IUserRepository _userRepository;
    protected readonly IWarehouseRepository _warehouseRepository;

    public NhanhHelper(
        ICryptoRepository cryptoRepository,
        ISyncServiceConfigRepository syncConfigRepository,
        ISyncServiceHelper syncServiceHelper,
        IMapper mapper,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        ICategoryRepository categoryRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IWarehouseRepository warehouseRepository
    )
    {
        _cryptoRepository = cryptoRepository;
        _syncConfigRepository = syncConfigRepository;
        _syncServiceHelper = syncServiceHelper;
        _mapper = mapper;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _categoryRepository = categoryRepository;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _warehouseRepository = warehouseRepository;
    }

    public async Task<Result<SyncServiceConfig>> SaveNhanhConfig(SyncServiceConfigDto dto)
    {
        try
        {
            // Mã hóa SecretKey trước khi lưu vào database
            string encryptedSecretKey = _cryptoRepository.Encrypt(dto.SecretKey);

            var config = new SyncServiceConfig
            {
                ShopId = dto.ShopId,
                SyncService = SyncServiceEnum.NhanhVN,
                AppId = dto.AppId,
                SecretKey = encryptedSecretKey,
                Status = TypeStatus.InActived,
                AdditionalConfig = dto.AdditionalConfig
            };

            var savedConfig = await _syncConfigRepository.CreateOrUpdate(config);
            return Result<SyncServiceConfig>.Success(savedConfig);
        }
        catch (Exception ex)
        {
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_SAVE_ERROR");
        }
    }
    public async Task<SyncServiceConfig> GetNhanhConfig(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        return config;
    }

    public async Task<Result<bool>> DeleteNhanhConfig(string shopId)
    {
        try
        {
            var config = await GetNhanhConfig(shopId);
            if (config == null) return Result<bool>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            var deleted = await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CONFIG_DELETE_ERROR");
        }
    }

    public async Task<Result<SyncServiceConfig>> UpdateNhanhAccessCode(string shopId, string accessCode)
    {
        var config = await GetNhanhConfig(shopId);
        if (config == null)
            return Result<SyncServiceConfig>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);
        await _syncConfigRepository.UpdateAccessCode(shopId, SyncServiceEnum.NhanhVN, accessCode);

        var tokenResponse = await _syncConfigRepository.GetAccessTokenAsync(SyncServiceEnum.NhanhVN, config.AppId, accessCode, config.SecretKey) as NhanhAccessTokenResponseDto;
        if (!string.IsNullOrEmpty(tokenResponse?.AccessToken))
        {
            await _syncConfigRepository.UpdateAccessToken(shopId, SyncServiceEnum.NhanhVN, tokenResponse.AccessToken);
        }
        if (tokenResponse?.BusinessId != null)
        {
            await _syncConfigRepository.UpdateBusinessId(shopId, SyncServiceEnum.NhanhVN, tokenResponse.BusinessId);
        }
        if (string.IsNullOrEmpty(config.VerifyToken))
        {
            var verifyToken = TokenUtil.GenerateRandomVerifyToken();
            await _syncConfigRepository.UpdateVerifyToken(shopId, SyncServiceEnum.NhanhVN, verifyToken);
        }

        // Cập nhật trạng thái config thành actived sau khi hoàn thành tất cả các bước cập nhật
        await _syncConfigRepository.UpdateStatus(shopId, SyncServiceEnum.NhanhVN, TypeStatus.Actived);

        return Result<SyncServiceConfig>.Success(config);
    }

    public async Task<Result<bool>> SyncNhanhProductFromWebhook(NhanhProductWebhookDto productData, string shopId)
    {
        // Lấy categories từ Nhanh.vn
        var categories = await GetNhanhCategoriesAsync(shopId);
        if (!categories.IsSuccess)
            return Result<bool>.Failure("SYNC_NHANH_CATEGORIES_ERROR");

        return await SyncNhanhProductFromWebhookWithCategories(productData, shopId, categories.Data);
    }

    public async Task<Result<bool>> SyncNhanhProductFromWebhookWithCategories(NhanhProductWebhookDto productData, string shopId, List<NhanhProductCategoryDto> categories)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

        var categoryObj = FindCategoryById(categories, productData.CategoryId);
        if (categoryObj == null)
            return Result<bool>.Failure("SYNC_NHANH_CATEGORY_NOT_FOUND");

        // Tạo hoặc tìm category
        var category = await _syncServiceHelper.FindCategoryByExternalId(categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (category == null)
        {
            await CreateCategoryWithHierarchy(categoryObj, shop);
        }

        // Lấy tất cả category IDs bao gồm cả children nếu có
        var categoryIds = await GetAllCategoryIdsFromHierarchy(categoryObj, shop);

        // Xử lý theo loại sản phẩm
        if (productData.ParentId > 0)
        {
            // Đây là sản phẩm biến thể, tạo Items với IsVariant = true
            await HandleVariantProduct(productData, shopId, categoryIds);
        }
        else if (productData.ParentId == -1)
        {
            // Đây là sản phẩm độc lập (không có biến thể), tạo Items với IsVariant = false
            HandleStandaloneProduct(productData, shopId, categoryIds);
        }
        // Bỏ qua sản phẩm chính có biến thể (parentId = -2) vì chỉ các variant mới được tạo thành Items

        return Result<bool>.Success(true);
    }

    public async Task<Result<bool>> SyncNhanhOrderFromWebhook(NhanhOrderDataDto orderData, string shopId)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

        var existingOrder = _syncServiceHelper.FindOrderByExternalId(orderData.OrderId.ToString(), SyncServiceEnum.NhanhVN);
        var nhanhCustomer = await GetNhanhCustomerByIdAsync(shopId, orderData.CustomerId);
        User user = EnsureUserFromNhanhCustomer(nhanhCustomer.Data, shopId);
        var listItems = new List<ItemsOrder>();
        foreach (var prod in orderData.Products ?? new List<NhanhOrderProductDto>())
        {
            var item = _syncServiceHelper.FindItemByExternalId(prod.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (item != null)
            {
                ItemsOrder itemsOrder = _mapper.Map<ItemsOrder>(item);
                itemsOrder.Quantity = prod.Quantity;
                itemsOrder.TotalBeforeTax = (decimal)prod.Price;
                itemsOrder.TotalAfterTax = (decimal)prod.Price;
                listItems.Add(itemsOrder);
            }
        }

        var shippingAddress = new ShippingAddress
        {
            Id = user.Id,
            UserId = user.UserId,
            ProvinceName = orderData.CustomerCity,
            DistrictName = orderData.CustomerDistrict,
            WardName = orderData.CustomerWard,
            Address = orderData.CustomerAddress,
            FullName = orderData.CustomerName,
            PhoneNumber = orderData.CustomerMobile,
        };

        // Tính tổng giá từ các items
        var totalPrice = listItems.Sum(item => (item.Price ?? 0) * (item.Quantity ?? 0));

        if (existingOrder != null)
        {
            // Update existing order
            existingOrder.ListItems = listItems;
            existingOrder.UserShippingAddress = shippingAddress;
            existingOrder.Creator = shippingAddress;
            existingOrder.StatusOrder = MapOrderStatusFromNhanhWebhook(orderData.Status);
            existingOrder.StatusTransport = MapTransportStatusFromNhanhWebhook(orderData.Status);
            existingOrder.StatusPay = MapPaymentStatusFromNhanhWebhook(orderData.Status, orderData.MoneyTransfer, orderData.MoneyDeposit);
            existingOrder.Price = totalPrice;
            existingOrder.TransportPrice = (long)orderData.ShipFee;
            existingOrder.TotalAfterTax = (decimal)orderData.CalcTotalMoney;
            existingOrder.Notes = orderData.Description;
            // Cập nhật các trường vận chuyển từ webhook
            existingOrder.TransportService = MapCarrierToTransportService(orderData.CarrierName);
            existingOrder.TransportOrderId = orderData.CarrierCode ?? "";
            existingOrder.Updated = DateTime.UtcNow;
            _orderRepository.UpdateOrder(existingOrder);
        }
        else
        {
            // Thêm mới đơn hàng
            var order = new Order
            {
                OrderId = Guid.NewGuid().ToString(),
                ShopId = shopId,
                TransactionId = Guid.NewGuid().ToString(),
                PartnerId = shop.PartnerId,
                OrderNo = Id64.Generator(),
                ListItems = listItems,
                Price = totalPrice,
                Notes = orderData.Description,
                StatusDelivery = TypeDelivery.ExpressDelivery,
                OrderOrigin = TypeOrigin.WebPartner,
                StatusOrder = MapOrderStatusFromNhanhWebhook(orderData.Status),
                StatusTransport = MapTransportStatusFromNhanhWebhook(orderData.Status),
                StatusPay = MapPaymentStatusFromNhanhWebhook(orderData.Status, orderData.MoneyTransfer, orderData.MoneyDeposit),
                TransportService = MapCarrierToTransportService(orderData.CarrierName),
                TransportOrderId = orderData.CarrierCode ?? "",
                TransportPrice = (long)orderData.ShipFee,
                TotalAfterTax = (decimal)orderData.CalcTotalMoney,
                UserShippingAddress = shippingAddress,
                Creator = shippingAddress,
                Created = DateTime.TryParse(orderData.CreatedDateTime, out var created) ? created : DateTime.Now,
                Status = TypeStatus.Actived,
                ExternalSource = SyncServiceEnum.NhanhVN,
                ExternalId = orderData.OrderId.ToString(),
            };
            await _orderRepository.CreateOrder(order);
        }

        return Result<bool>.Success(true);
    }
    private User EnsureUserFromNhanhCustomer(NhanhSyncCustomerDto nhanhCustomer, string shopId)
    {
        User? user = null;
        if (!string.IsNullOrEmpty(nhanhCustomer.Mobile))
            user = _userRepository.FindByUserPhone(shopId, nhanhCustomer.Mobile);

        if (user == null)
        {
            user = new User
            {
                UserId = Guid.NewGuid().ToString(),
                ShopId = shopId,
                Email = nhanhCustomer.Email,
                PhoneNumber = nhanhCustomer.Mobile,
                Fullname = nhanhCustomer.Name,
                Address = nhanhCustomer.Address,
                Status = TypeStatus.Actived,
                Created = DateTime.Now,
            };
            _userRepository.CreateUser(user);
        }
        else
        {
            user.Email = nhanhCustomer.Email;
            user.Fullname = nhanhCustomer.Name;
            user.PhoneNumber = nhanhCustomer.Mobile;
            user.Address = nhanhCustomer.Address;
            user.Updated = DateTime.Now;
            _userRepository.UpdateUser(user);
        }
        return user;
    }
    private async Task<Result<NhanhSyncCustomerDto>> GetNhanhCustomerByIdAsync(string shopId, int customerId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCustomer;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" },
            { new StringContent(JsonConvert.SerializeObject(new { id = customerId })), "data" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCustomerSearchData>>(json);

        if (apiResponse.Code != 1)
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_API_ERROR");

        if (apiResponse.Data?.Customers == null || !apiResponse.Data.Customers.Any())
            return Result<NhanhSyncCustomerDto>.Failure("SYNC_NHANH_CUSTOMER_NOT_FOUND");

        // Lấy customer đầu tiên từ dictionary
        var customerDto = apiResponse.Data.Customers.Values.First();
        return Result<NhanhSyncCustomerDto>.Success(customerDto);
    }
    public async Task<Result<bool>> SyncNhanhCustomerFromWebhook(object customerData, string shopId)
    {
        try
        {
            // TODO: Implement customer sync logic for NhanhVN
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_CUSTOMER_SYNC_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhProductsFromWebhook(object productIds)
    {
        try
        {
            if (productIds is List<int> nhanhProductIds)
            {
                foreach (var productId in nhanhProductIds)
                {
                    var existingItem = _syncServiceHelper.FindItemByExternalId(productId.ToString(), SyncServiceEnum.NhanhVN);
                    if (existingItem != null)
                    {
                        _itemsRepository.DeleteItems(existingItem.ItemsId);
                    }
                }
            }
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_PRODUCT_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> DeleteNhanhOrderFromWebhook(object orderData, string shopId)
    {
        try
        {
            var nhanhOrder = JsonConvert.DeserializeObject<NhanhOrderDataDto>(orderData.ToString());
            var existingOrder = _syncServiceHelper.FindOrderByExternalId(nhanhOrder.OrderId.ToString(), SyncServiceEnum.NhanhVN);

            if (existingOrder != null)
            {
                _orderRepository.DeleteOrder(existingOrder.OrderId);
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure("SYNC_NHANH_ORDER_DELETE_ERROR");
        }
    }

    public async Task<Result<bool>> CreateOrderToNhanh(Order order, string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<bool>.Failure("SYNC_NHANH_SHOP_NOT_CONFIGURED");

        // Giải mã SecretKey để sử dụng
        config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

        // Tạo data object cho order
        var orderData = new
        {
            id = order.OrderNo,
            customerName = order.UserShippingAddress?.FullName,
            customerMobile = order.UserShippingAddress?.PhoneNumber,
            customerAddress = order.UserShippingAddress?.Address,
            customerCityName = order.UserShippingAddress?.ProvinceName,
            customerDistrictName = order.UserShippingAddress?.DistrictName,
            customerWardName = order.UserShippingAddress?.WardName,
            paymentMethod = order.TypePay == TypePayment.COD ? "COD" : "Online",
            customerShipFee = order.TransportPrice,
            status = order.StatusOrder == TypeOrderStatus.Pending ? "New" : order.StatusOrder == TypeOrderStatus.Verified ? "Confirmed" : "Confirming",
            description = order.Notes,
            productList = order.ListItems?.Select(x => new
            {
                id = x.ItemsId,
                name = x.ItemsName,
                code = x.ItemsCode,
                idNhanh = x.ExternalId ?? null,
                quantity = x.Quantity,
                price = x.Price
            }).ToList(),
        };

        try
        {
            using var httpClient = new HttpClient();

            // Sử dụng MultipartFormDataContent như trong curl command
            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(orderData)), "data" }
            };

            // Sử dụng đúng URL cho create order
            var response = await httpClient.PostAsync(NhanhConstants.ApiCreateOrder, form);

            if (!response.IsSuccessStatusCode)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_HTTP_ERROR");

            var json = await response.Content.ReadAsStringAsync();
            if (string.IsNullOrWhiteSpace(json))
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_EMPTY_RESPONSE");

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhCreateOrderResponseDto>>(json);

            if (apiResponse?.Code != 1)
                return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");

            // Lưu thông tin response vào Order entity
            if (apiResponse.Data != null)
            {
                order.ExternalSource = SyncServiceEnum.NhanhVN;
                order.ExternalId = apiResponse.Data.OrderId.ToString();
                _orderRepository.UpdateOrder(order);
            }

            return Result<bool>.Success(true);
        }
        catch (JsonException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_INVALID_JSON");
        }
        catch (HttpRequestException)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_NETWORK_ERROR");
        }
        catch (Exception)
        {
            return Result<bool>.Failure("SYNC_NHANH_CREATE_ORDER_API_ERROR");
        }
    }

    public async Task<Result<List<NhanhProductCategoryDto>>> GetNhanhCategoriesAsync(string shopId)
    {
        var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
        if (config == null || string.IsNullOrEmpty(config.AccessToken))
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

        using var httpClient = new HttpClient();
        var urlCategory = NhanhConstants.ApiCategory;

        var form = new MultipartFormDataContent
        {
            { new StringContent("2.0"), "version" },
            { new StringContent(config.AppId), "appId" },
            { new StringContent(config.BusinessId.ToString()), "businessId" },
            { new StringContent(config.AccessToken), "accessToken" }
        };

        var response = await httpClient.PostAsync(urlCategory, form);
        response.EnsureSuccessStatusCode();
        var json = await response.Content.ReadAsStringAsync();
        var obj = JObject.Parse(json);
        if ((int)obj["code"] != 1)
            return Result<List<NhanhProductCategoryDto>>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");
        var categories = obj["data"].ToObject<List<NhanhProductCategoryDto>>();
        return Result<List<NhanhProductCategoryDto>>.Success(categories);

    }

    public NhanhProductCategoryDto FindCategoryById(List<NhanhProductCategoryDto> categories, int categoryId)
    {
        foreach (var category in categories)
        {

            if (category.Id == categoryId)
                return category;
            if (category.Childs != null && category.Childs.Count > 0)
            {
                var found = FindCategoryById(category.Childs, categoryId);
                if (found != null)
                    return found;
            }
        }
        return null;
    }

    public async Task<Category> CreateCategoryWithHierarchy(NhanhProductCategoryDto categoryDto, Shop shop, string parentCategoryId = null, int level = 1)
    {
        // Kiểm tra xem category đã tồn tại chưa
        var existingCategory = await _categoryRepository.FindByExternalId(categoryDto.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (existingCategory != null)
            return existingCategory;

        // Nếu có ParentId trong Nhanh và chưa có parentCategoryId, tìm hoặc tạo parent category
        if (categoryDto.ParentId > 0 && string.IsNullOrEmpty(parentCategoryId))
        {
            var parentCategory = await _categoryRepository.FindByExternalId(categoryDto.ParentId.ToString(), SyncServiceEnum.NhanhVN);
            if (parentCategory == null)
            {
                // Tìm parent category trong danh sách categories từ Nhanh
                var categories = await GetNhanhCategoriesAsync(shop.ShopId);
                var parentCategoryDto = FindCategoryById(categories.Data, categoryDto.ParentId);
                if (parentCategoryDto != null)
                {
                    parentCategory = await CreateCategoryWithHierarchy(parentCategoryDto, shop, null, level - 1);
                }
            }
            parentCategoryId = parentCategory?.CategoryId;
        }

        // Tạo category mới
        var newCategory = new Category
        {
            CategoryId = Guid.NewGuid().ToString(),
            PartnerId = shop.PartnerId,
            CategoryName = categoryDto.Name,
            ShopId = shop.ShopId,
            ParentId = parentCategoryId,
            Image = new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = categoryDto.Image
            },
            CategoryType = TypeCategory.Product,
            CategoryLevel = level.ToString(),
            CategoryPosition = categoryDto.Order,
            Publish = TypeCategoryPublish.Publish,
            Active = TypeRuleActive.Actived,
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = categoryDto.Id.ToString(),
            Created = DateTime.UtcNow,
            Updated = DateTime.UtcNow
        };

        var createdCategory = await _categoryRepository.CreateCategory(newCategory);

        // Tạo các child categories nếu có
        if (categoryDto.Childs != null && categoryDto.Childs.Count > 0)
        {
            foreach (var childDto in categoryDto.Childs)
            {
                await CreateCategoryWithHierarchy(childDto, shop, createdCategory.CategoryId, level + 1);
            }
        }

        return createdCategory;
    }
    public async Task<List<string>> GetAllCategoryIdsFromHierarchy(NhanhProductCategoryDto categoryObj, Shop shop)
    {
        var categoryIds = new List<string>();

        // Thêm category chính
        var mainCategory = await _syncServiceHelper.FindCategoryByExternalId(categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
        if (mainCategory == null)
        {
            mainCategory = await CreateCategoryWithHierarchy(categoryObj, shop);
        }
        categoryIds.Add(mainCategory.CategoryId);

        // Thêm tất cả children categories nếu có
        if (categoryObj.Childs != null && categoryObj.Childs.Count > 0)
        {
            var childCategoryIds = await GetChildCategoryIds(categoryObj.Childs, shop);
            categoryIds.AddRange(childCategoryIds);
        }

        return categoryIds;
    }

    public async Task<List<string>> GetChildCategoryIds(List<NhanhProductCategoryDto> childCategories, Shop shop)
    {
        var categoryIds = new List<string>();

        foreach (var childCategory in childCategories)
        {
            var category = await _syncServiceHelper.FindCategoryByExternalId(childCategory.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (category == null)
            {
                category = await CreateCategoryWithHierarchy(childCategory, shop);
            }
            categoryIds.Add(category.CategoryId);

            // Đệ quy để lấy children của children
            if (childCategory.Childs != null && childCategory.Childs.Count > 0)
            {
                var subChildCategoryIds = await GetChildCategoryIds(childCategory.Childs, shop);
                categoryIds.AddRange(subChildCategoryIds);
            }
        }

        return categoryIds;
    }

    public ProductDto MapNhanhProductToProductDto(NhanhProductWebhookDto nhanhProduct, string shopId, List<string> categoryIds)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        // Lấy WarehouseId đầu tiên của shop, nếu không có thì null
        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();
        var warehouseId = warehouse?.WarehouseId;
        // Tạo danh sách ảnh từ cả image và images
        var imageList = new List<MediaInfo>();

        // Thêm ảnh đại diện nếu có
        if (!string.IsNullOrEmpty(nhanhProduct.Image))
        {
            imageList.Add(new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = nhanhProduct.Image
            });
        }

        // Thêm các ảnh khác nếu có
        if (nhanhProduct.Images != null && nhanhProduct.Images.Any())
        {
            foreach (var img in nhanhProduct.Images)
            {
                if (!string.IsNullOrEmpty(img))
                {
                    imageList.Add(new MediaInfo
                    {
                        MediaFileId = Guid.NewGuid().ToString(),
                        Type = TypeMedia.IMAGE,
                        Link = img
                    });
                }
            }
        }
        return new ProductDto
        {
            PartnerId = shop?.PartnerId,
            ItemsName = nhanhProduct.Name,
            ItemsCode = !string.IsNullOrEmpty(nhanhProduct.Code) ? nhanhProduct.Code : $"NHANH_{nhanhProduct.ProductId}",
            ExternalSource = SyncServiceEnum.NhanhVN,
            CategoryIds = categoryIds,
            Price = (long?)nhanhProduct.Price,
            PriceReal = (long?)nhanhProduct.Price,
            PriceCapital = (long?)nhanhProduct.Price,
            Quantity = nhanhProduct.Inventory?.Available ?? 0,
            QuantityPurchase = null,
            Images = imageList,
            ItemsInfo = nhanhProduct.Description,
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ItemsHeight = nhanhProduct.Height,
            ItemsWidth = nhanhProduct.Width,
            ItemsLength = nhanhProduct.Length,
            ItemsWeight = nhanhProduct.Weight,
            CustomTaxRate = nhanhProduct.Vat,
            IsTop = false,
            IsShow = true,
            TypePublish = TypePublish.Publish,
            WarehouseId = warehouseId,
            Status = nhanhProduct.Status == "Inactive" ? TypeStatus.InActived : TypeStatus.Actived,
            IsVariant = false, // Sẽ được set trong HandleMainProduct hoặc HandleVariantProduct
            ListVariant = new List<VariantBase>(), // Không sử dụng ListVariant trong logic mới
        };
    }

    public static TypeOrderStatus MapOrderStatusFromNhanhWebhook(string status)
    {
        if (string.IsNullOrEmpty(status))
            return TypeOrderStatus.Pending;

        return status.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingfordelivery" or
            "waitingforpickup" or "waitingforpayment" or "waitingforstock" or
            "waitingforconfirm" or "waitingforreturn" or "waitingforrefund" => TypeOrderStatus.Pending,
            "confirmed" => TypeOrderStatus.Verified,
            "delivering" => TypeOrderStatus.Pending,
            "delivered" or "success" => TypeOrderStatus.Success,
            "canceled" => TypeOrderStatus.Failed,
            "return" => TypeOrderStatus.Refund,
            "paid" => TypeOrderStatus.Paid,
            "refund" => TypeOrderStatus.Refund,
            _ => TypeOrderStatus.Pending
        };
    }

    public static TypeTransportStatus MapTransportStatusFromNhanhWebhook(string status)
    {
        if (string.IsNullOrEmpty(status))
            return TypeTransportStatus.Created;

        return status.Trim().ToLower() switch
        {
            "new" or "confirming" or "customerconfirming" or "waitingforpayment" or
            "waitingforstock" or "waitingforconfirm" => TypeTransportStatus.Created,
            "confirmed" => TypeTransportStatus.Verified,
            "waitingfordelivery" or "waitingforpickup" => TypeTransportStatus.WaitingForDelivery,
            "delivering" => TypeTransportStatus.Delivering,
            "delivered" => TypeTransportStatus.Delivered,
            "success" => TypeTransportStatus.Success,
            "canceled" => TypeTransportStatus.Cancel,
            "return" or "waitingforreturn" => TypeTransportStatus.Refunding,
            "refund" or "waitingforrefund" => TypeTransportStatus.Refunded,
            _ => TypeTransportStatus.Created
        };
    }

    /// <summary>
    /// Map trạng thái thanh toán từ NhanhVN webhook
    /// </summary>
    /// <param name="status">Trạng thái đơn hàng từ NhanhVN</param>
    /// <param name="moneyTransfer">Tiền chuyển khoản</param>
    /// <param name="moneyDeposit">Tiền đặt cọc</param>
    /// <returns>Trạng thái thanh toán</returns>
    public static TypePayStatus MapPaymentStatusFromNhanhWebhook(string status, double? moneyTransfer, double moneyDeposit)
    {
        if (string.IsNullOrEmpty(status))
            return TypePayStatus.NotPaid;

        var statusLower = status.Trim().ToLower();

        // Kiểm tra trạng thái hoàn tiền trước
        if (statusLower == "refund" || statusLower == "waitingforrefund")
            return TypePayStatus.Refund;

        // Kiểm tra trạng thái đã thanh toán
        if (statusLower == "paid")
            return TypePayStatus.Paid;

        // Kiểm tra có tiền chuyển khoản hoặc đặt cọc
        var totalPaidAmount = (moneyTransfer ?? 0) + moneyDeposit;
        if (totalPaidAmount > 0)
            return TypePayStatus.Paid;

        // Các trạng thái khác mặc định là chưa thanh toán
        return TypePayStatus.NotPaid;
    }

    /// <summary>
    /// Lấy danh sách sản phẩm từ Nhanh.vn API
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="page">Trang hiện tại (mặc định: 1)</param>
    /// <returns>Response chứa danh sách sản phẩm và thông tin phân trang từ Nhanh.vn</returns>
    public async Task<Result<NhanhProductSearchResponse>> GetNhanhProductsAsync(string shopId, int page)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            using var httpClient = new HttpClient();
            var url = NhanhConstants.ApiProduct;

            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(new { page = page })), "data" }};

            var response = await httpClient.PostAsync(url, form);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();
            Console.WriteLine(json);

            // Sử dụng JsonSerializerSettings để ignore errors khi deserialize
            var settings = new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                Error = (sender, args) =>
                {
                    args.ErrorContext.Handled = true;
                }
            };

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhProductSearchResponse>>(json, settings);

            if (apiResponse.Code != 1)
                return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_PRODUCT_API_ERROR");

            if (apiResponse.Data == null)
                return Result<NhanhProductSearchResponse>.Success(new NhanhProductSearchResponse
                {
                    CurrentPage = page,
                    TotalPages = 0,
                    Products = new Dictionary<string, NhanhProductDetailDto>()
                });

            // Debug: Log thông tin pagination
            Console.WriteLine($"API Response - CurrentPage: {apiResponse.Data.CurrentPage}, TotalPages: {apiResponse.Data.TotalPages}, Products Count: {apiResponse.Data.Products?.Count}");

            return Result<NhanhProductSearchResponse>.Success(apiResponse.Data);
        }
        catch (Exception ex)
        {
            return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_PRODUCT_API_EXCEPTION");
        }
    }

    /// <summary>
    /// Lấy attribute name theo index từ cấu trúc attributes của Nhanh Webhook
    /// </summary>
    /// <param name="attributes">Danh sách attributes từ Nhanh Webhook</param>
    /// <param name="index">Index của attribute cần lấy (0, 1, 2)</param>
    /// <returns>Attribute name hoặc null nếu không tìm thấy</returns>
    private string GetAttributeNameByIndex(List<NhanhProductAttributeDto> attributes, int index)
    {
        if (attributes == null || index >= attributes.Count)
            return null;

        return attributes[index]?.AttributeName;
    }

    /// <summary>
    /// Lấy attribute value theo index từ cấu trúc attributes của Nhanh Webhook
    /// </summary>
    /// <param name="attributes">Danh sách attributes từ Nhanh Webhook</param>
    /// <param name="index">Index của attribute cần lấy (0, 1, 2)</param>
    /// <returns>Attribute value hoặc null nếu không tìm thấy</returns>
    private string GetAttributeValueByIndex(List<NhanhProductAttributeDto> attributes, int index)
    {
        if (attributes == null || index >= attributes.Count)
            return null;

        return attributes[index]?.Name; // Sử dụng Name vì đây là giá trị hiển thị
    }

    /// <summary>
    /// Lấy tất cả variants của cùng 1 parent product từ Nhanh.vn
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="parentId">ID của parent product</param>
    /// <returns>Danh sách tất cả variants của parent</returns>
    private async Task<List<NhanhProductDetailDto>> GetAllVariantsOfParent(string shopId, int parentId)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return new List<NhanhProductDetailDto>();

            using var httpClient = new HttpClient();
            var url = NhanhConstants.ApiProduct;

            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(new { parentId = parentId })), "data" }
            };

            var response = await httpClient.PostAsync(url, form);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();

            var settings = new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                Error = (sender, args) => { args.ErrorContext.Handled = true; }
            };

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhProductSearchResponse>>(json, settings);

            if (apiResponse.Code != 1 || apiResponse.Data?.Products == null)
                return new List<NhanhProductDetailDto>();

            return apiResponse.Data.Products.Values.ToList();
        }
        catch (Exception)
        {
            return new List<NhanhProductDetailDto>();
        }
    }

    /// <summary>
    /// Lấy thông tin 1 sản phẩm theo ID từ Nhanh.vn API
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="productId">ID của sản phẩm cần lấy</param>
    /// <returns>Thông tin sản phẩm từ Nhanh.vn</returns>
    public async Task<Result<NhanhProductDetailDto>> GetNhanhProductByIdAsync(string shopId, int productId)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<NhanhProductDetailDto>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            using var httpClient = new HttpClient();
            var url = NhanhConstants.ApiProduct;

            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(new { id = productId })), "data" }
            };

            var response = await httpClient.PostAsync(url, form);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();

            // Sử dụng JsonSerializerSettings để ignore errors khi deserialize
            var settings = new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                Error = (sender, args) =>
                {
                    args.ErrorContext.Handled = true;
                }
            };

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhProductSearchResponse>>(json, settings);

            if (apiResponse.Code != 1)
                return Result<NhanhProductDetailDto>.Failure("SYNC_NHANH_PRODUCT_API_ERROR");

            if (apiResponse.Data?.Products == null || !apiResponse.Data.Products.Any())
                return Result<NhanhProductDetailDto>.Failure("SYNC_NHANH_PRODUCT_NOT_FOUND");

            // Lấy sản phẩm đầu tiên từ dictionary (vì query theo ID chỉ trả về 1 sản phẩm)
            var product = apiResponse.Data.Products.Values.First();
            return Result<NhanhProductDetailDto>.Success(product);
        }
        catch (Exception ex)
        {
            return Result<NhanhProductDetailDto>.Failure("SYNC_NHANH_PRODUCT_API_EXCEPTION");
        }
    }

    /// <summary>
    /// Xử lý sản phẩm biến thể (parentId > 0)
    /// </summary>
    private async Task HandleVariantProduct(NhanhProductWebhookDto variantData, string shopId, List<string> categoryIds)
    {
        // Lấy thông tin sản phẩm cha bằng cách gọi API
        var parentProductResult = await GetNhanhProductByIdAsync(shopId, variantData.ParentId);
        if (!parentProductResult.IsSuccess)
        {
            Console.WriteLine($"Không thể lấy thông tin sản phẩm cha {variantData.ParentId} cho variant {variantData.ProductId}");
            return;
        }

        var parentProduct = parentProductResult.Data;

        // Lấy tất cả variants của cùng parent để xử lý images
        var allVariantsOfParent = await GetAllVariantsOfParent(shopId, variantData.ParentId);

        // Tạo ItemsCode dựa trên ParentId (sản phẩm chính)
        var itemsCode = !string.IsNullOrEmpty(parentProduct.Code) ? parentProduct.Code : $"NHANH_{variantData.ParentId}";

        // Kiểm tra xem variant này đã tồn tại chưa
        var existingVariant = _syncServiceHelper.FindItemByExternalId(variantData.ProductId.ToString(), SyncServiceEnum.NhanhVN);

        if (existingVariant != null)
        {
            // Update existing variant
            UpdateExistingVariantItem(existingVariant, variantData, parentProduct, allVariantsOfParent, shopId, categoryIds);
            _itemsRepository.UpdateItems(existingVariant);
        }
        else
        {
            // Create new variant item
            var variantItem = CreateVariantItemFromNhanhData(variantData, parentProduct, allVariantsOfParent, itemsCode, shopId, categoryIds);
            _itemsRepository.CreateItems(variantItem);
        }
    }

    /// <summary>
    /// Tạo variant item từ dữ liệu Nhanh
    /// </summary>
    private Items CreateVariantItemFromNhanhData(NhanhProductWebhookDto variantData, NhanhProductDetailDto parentProduct, List<NhanhProductDetailDto> allVariantsOfParent, string itemsCode, string shopId, List<string> categoryIds)
    {
        var shop = _shopRepository.FindByShopId(shopId);

        // Lấy WarehouseId đầu tiên của shop
        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();
        var warehouseId = warehouse?.WarehouseId;

        var variantItem = new Items
        {
            ItemsId = Guid.NewGuid().ToString(),
            ItemsCode = itemsCode, // Sử dụng ItemsCode được truyền vào
            PartnerId = shop?.PartnerId,
            ShopId = shopId,
            ItemsType = TypeItems.Product,
            CategoryIds = categoryIds,
            ItemsName = parentProduct.Name, // Lấy tên từ sản phẩm cha
            ItemsInfo = variantData.Description, // Vẫn dùng description từ variant
            Price = (long?)variantData.Price,
            PriceReal = (long?)variantData.Price,
            PriceCapital = (long?)variantData.Price,
            Quantity = variantData.Inventory?.Available ?? 0,
            QuantityPurchase = null,
            ItemsHeight = variantData.Height,
            ItemsWidth = variantData.Width,
            ItemsLength = variantData.Length,
            ItemsWeight = variantData.Weight,
            CustomTaxRate = variantData.Vat,
            IsTop = false,
            IsShow = true,
            TypePublish = TypePublish.Publish,
            WarehouseId = warehouseId,
            Status = variantData.Status == "Inactive" ? TypeStatus.InActived : TypeStatus.Actived,
            IsVariant = true, // Tất cả variant items đều có IsVariant = true
            Created = DateTime.Now,
            Updated = DateTime.Now,
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = variantData.ProductId.ToString()
        };

        // Set variant properties từ attributes
        if (variantData.Attributes != null && variantData.Attributes.Count > 0)
        {
            variantItem.VariantNameOne = GetAttributeNameByIndex(variantData.Attributes, 0);
            variantItem.VariantValueOne = GetAttributeValueByIndex(variantData.Attributes, 0);
            variantItem.VariantNameTwo = GetAttributeNameByIndex(variantData.Attributes, 1);
            variantItem.VariantValueTwo = GetAttributeValueByIndex(variantData.Attributes, 1);
            variantItem.VariantNameThree = GetAttributeNameByIndex(variantData.Attributes, 2);
            variantItem.VariantValueThree = GetAttributeValueByIndex(variantData.Attributes, 2);
        }

        // Set Images: Tất cả images của các variants cùng parent
        var allImages = GetAllImagesFromVariants(allVariantsOfParent);
        variantItem.Images = allImages;

        // Set VariantImage: Image riêng của variant hiện tại, nếu không có thì lấy của variant có cùng attributes đầu tiên
        var variantImage = GetVariantImage(variantData, allVariantsOfParent);
        variantItem.VariantImage = variantImage;

        return variantItem;
    }

    /// <summary>
    /// Lấy tất cả images từ các variants của cùng parent
    /// </summary>
    private List<MediaInfo> GetAllImagesFromVariants(List<NhanhProductDetailDto> allVariants)
    {
        var allImages = new List<MediaInfo>();
        var addedImageLinks = new HashSet<string>(); // Để tránh duplicate

        foreach (var variant in allVariants)
        {
            // Thêm image chính của variant
            if (!string.IsNullOrEmpty(variant.Image) && !addedImageLinks.Contains(variant.Image))
            {
                allImages.Add(new MediaInfo
                {
                    MediaFileId = Guid.NewGuid().ToString(),
                    Type = TypeMedia.IMAGE,
                    Link = variant.Image
                });
                addedImageLinks.Add(variant.Image);
            }

            // Note: NhanhProductDetailDto chỉ có field Image, không có Images array
            // Nếu cần thêm nhiều images, có thể cần cập nhật DTO hoặc gọi API khác
        }

        return allImages;
    }

    /// <summary>
    /// Lấy VariantImage cho variant hiện tại
    /// </summary>
    private MediaInfo GetVariantImage(NhanhProductWebhookDto currentVariant, List<NhanhProductDetailDto> allVariants)
    {
        // Tìm variant tương ứng trong allVariants
        var matchingVariant = allVariants.FirstOrDefault(v => v.IdNhanh == currentVariant.ProductId);

        // Nếu variant hiện tại có image, sử dụng image đó
        if (matchingVariant != null && !string.IsNullOrEmpty(matchingVariant.Image))
        {
            return new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = matchingVariant.Image
            };
        }

        // Nếu variant hiện tại có image từ webhook data
        if (!string.IsNullOrEmpty(currentVariant.Image))
        {
            return new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = currentVariant.Image
            };
        }

        // Nếu variant hiện tại không có image, tìm variant có cùng attribute đầu tiên
        if (currentVariant.Attributes != null && currentVariant.Attributes.Count > 0)
        {
            var firstAttributeValue = GetAttributeValueByIndex(currentVariant.Attributes, 0);

            if (!string.IsNullOrEmpty(firstAttributeValue))
            {
                // Tìm variant có cùng giá trị attribute đầu tiên và có image
                var similarVariant = allVariants.FirstOrDefault(v =>
                    !string.IsNullOrEmpty(v.Image) &&
                    HasSameFirstAttributeValue(v, firstAttributeValue));

                if (similarVariant != null)
                {
                    return new MediaInfo
                    {
                        MediaFileId = Guid.NewGuid().ToString(),
                        Type = TypeMedia.IMAGE,
                        Link = similarVariant.Image
                    };
                }
            }
        }

        // Fallback: Lấy image đầu tiên có sẵn từ các variants khác
        var firstVariantWithImage = allVariants.FirstOrDefault(v => !string.IsNullOrEmpty(v.Image));
        if (firstVariantWithImage != null)
        {
            return new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = firstVariantWithImage.Image
            };
        }

        // Nếu không có image nào, trả về null
        return null;
    }

    /// <summary>
    /// Kiểm tra xem variant có cùng giá trị attribute đầu tiên không
    /// </summary>
    private bool HasSameFirstAttributeValue(NhanhProductDetailDto variant, string attributeValue)
    {
        if (variant.Attributes == null || variant.Attributes.Count == 0)
            return false;

        var firstAttr = variant.Attributes[0];
        if (firstAttr == null || !firstAttr.Any())
            return false;

        // Lấy giá trị attribute đầu tiên từ variant
        var firstAttributeDetail = firstAttr.Values.FirstOrDefault();
        return firstAttributeDetail?.Name == attributeValue;
    }

    /// <summary>
    /// Lấy danh sách parent products (sản phẩm cha có biến thể) từ Nhanh.vn API
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="page">Trang hiện tại (mặc định: 1)</param>
    /// <returns>Response chứa danh sách parent products và thông tin phân trang từ Nhanh.vn</returns>
    public async Task<Result<NhanhProductSearchResponse>> GetNhanhParentProductsAsync(string shopId, int page)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            using var httpClient = new HttpClient();
            var url = NhanhConstants.ApiProduct;

            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(new { parentId = -2, page = page })), "data" }
            };

            var response = await httpClient.PostAsync(url, form);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();

            // Sử dụng JsonSerializerSettings để ignore errors khi deserialize
            var settings = new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                Error = (sender, args) =>
                {
                    args.ErrorContext.Handled = true;
                }
            };

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhProductSearchResponse>>(json, settings);

            if (apiResponse.Code != 1)
                return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_PARENT_PRODUCT_API_ERROR");

            if (apiResponse.Data == null)
                return Result<NhanhProductSearchResponse>.Success(new NhanhProductSearchResponse
                {
                    CurrentPage = page,
                    TotalPages = 0,
                    Products = new Dictionary<string, NhanhProductDetailDto>()
                });

            // Debug: Log thông tin pagination
            Console.WriteLine($"Parent Products API Response - Page: {apiResponse.Data.CurrentPage}, TotalPages: {apiResponse.Data.TotalPages}, Products Count: {apiResponse.Data.Products?.Count}");

            return Result<NhanhProductSearchResponse>.Success(apiResponse.Data);
        }
        catch (Exception ex)
        {
            return Result<NhanhProductSearchResponse>.Failure("SYNC_NHANH_PARENT_PRODUCT_API_EXCEPTION");
        }
    }

    /// <summary>
    /// Đồng bộ sản phẩm từ Nhanh webhook với parent products cache
    /// </summary>
    public async Task<Result<bool>> SyncNhanhProductFromWebhookWithParentCache(NhanhProductWebhookDto productData, string shopId, List<NhanhProductCategoryDto> categories, Dictionary<int, NhanhProductDetailDto> parentProductsCache)
    {
        Console.WriteLine(productData.ParentId);

        // Tìm categoryIds từ productData (sử dụng logic đã có)
        var categoryObj = categories?.FirstOrDefault(c => c.Id == productData.CategoryId);
        var categoryIds = new List<string>();
        if (categoryObj != null)
        {
            var shop = _shopRepository.FindByShopId(shopId);
            var mainCategory = await _syncServiceHelper.FindCategoryByExternalId(categoryObj.Id.ToString(), SyncServiceEnum.NhanhVN);
            if (mainCategory != null)
            {
                categoryIds.Add(mainCategory.CategoryId);
            }
        }

        // Xử lý theo loại sản phẩm
        if (productData.ParentId > 0)
        {
            // Đây là sản phẩm biến thể, tạo Items với IsVariant = true
            // Sử dụng parent products cache thay vì gọi API
            await HandleVariantProductWithCache(productData, shopId, categoryIds, parentProductsCache);
        }
        else if (productData.ParentId == -1)
        {
            // Đây là sản phẩm độc lập (không có biến thể), tạo Items với IsVariant = false
            HandleStandaloneProduct(productData, shopId, categoryIds);
        }
        // Bỏ qua sản phẩm chính có biến thể (parentId = -2) vì chỉ các variant mới được tạo thành Items

        return Result<bool>.Success(true);
    }

    /// <summary>
    /// Xử lý sản phẩm biến thể với parent products cache
    /// </summary>
    private async Task HandleVariantProductWithCache(NhanhProductWebhookDto variantData, string shopId, List<string> categoryIds, Dictionary<int, NhanhProductDetailDto> parentProductsCache)
    {
        // Lấy thông tin sản phẩm cha từ cache
        if (!parentProductsCache.TryGetValue(variantData.ParentId, out var parentProduct))
        {
            Console.WriteLine($"Không tìm thấy parent product {variantData.ParentId} trong cache cho variant {variantData.ProductId}");
            // Fallback: gọi API để lấy parent product
            var parentProductResult = await GetNhanhProductByIdAsync(shopId, variantData.ParentId);
            if (!parentProductResult.IsSuccess)
            {
                Console.WriteLine($"Không thể lấy thông tin sản phẩm cha {variantData.ParentId} cho variant {variantData.ProductId}");
                return;
            }
            parentProduct = parentProductResult.Data;
        }

        // Lấy tất cả variants của cùng parent để xử lý images
        var allVariantsOfParent = await GetAllVariantsOfParent(shopId, variantData.ParentId);

        // Tạo ItemsCode dựa trên ParentId (sản phẩm chính)
        var itemsCode = !string.IsNullOrEmpty(parentProduct.Code) ? parentProduct.Code : $"NHANH_{variantData.ParentId}";

        // Kiểm tra xem variant này đã tồn tại chưa
        var existingVariant = _syncServiceHelper.FindItemByExternalId(variantData.ProductId.ToString(), SyncServiceEnum.NhanhVN);

        if (existingVariant != null)
        {
            // Update existing variant
            UpdateExistingVariantItem(existingVariant, variantData, parentProduct, allVariantsOfParent, shopId, categoryIds);
            _itemsRepository.UpdateItems(existingVariant);
        }
        else
        {
            // Create new variant item
            var variantItem = CreateVariantItemFromNhanhData(variantData, parentProduct, allVariantsOfParent, itemsCode, shopId, categoryIds);
            _itemsRepository.CreateItems(variantItem);
        }
    }

    /// <summary>
    /// Xử lý sản phẩm độc lập (parentId = -1, không có biến thể)
    /// </summary>
    private void HandleStandaloneProduct(NhanhProductWebhookDto productData, string shopId, List<string> categoryIds)
    {
        // Kiểm tra xem sản phẩm này đã tồn tại chưa
        var existingProduct = _syncServiceHelper.FindItemByExternalId(productData.ProductId.ToString(), SyncServiceEnum.NhanhVN);

        if (existingProduct != null)
        {
            // Update existing standalone product
            UpdateExistingStandaloneProduct(existingProduct, productData, shopId, categoryIds);
            _itemsRepository.UpdateItems(existingProduct);
        }
        else
        {
            // Create new standalone product
            var standaloneItem = CreateStandaloneItemFromNhanhData(productData, shopId, categoryIds);
            _itemsRepository.CreateItems(standaloneItem);
        }
    }

    /// <summary>
    /// Tạo standalone item từ dữ liệu Nhanh
    /// </summary>
    private Items CreateStandaloneItemFromNhanhData(NhanhProductWebhookDto productData, string shopId, List<string> categoryIds)
    {
        Shop? shop = _shopRepository.FindByShopId(shopId);
        if (shop == null) return null;

        // Lấy WarehouseId đầu tiên của shop
        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();
        var warehouseId = warehouse?.WarehouseId;

        var standaloneItem = new Items
        {
            ItemsId = Guid.NewGuid().ToString(),
            PartnerId = shop.PartnerId,
            ShopId = shopId,
            ItemsCode = !string.IsNullOrEmpty(productData.Code) ? productData.Code : $"NHANH_{productData.ProductId}",
            CategoryIds = categoryIds,
            ItemsName = productData.Name,
            ItemsInfo = productData.Description,
            Price = (long?)productData.Price,
            WarehouseId = warehouseId,
            Status = productData.Status == "Inactive" ? TypeStatus.InActived : TypeStatus.Actived,
            IsVariant = false, // Sản phẩm độc lập không phải variant

            // Set images từ productData
            Images = CreateImageListFromProductData(productData),
            VariantImage = CreateVariantImageFromProductData(productData),

            // Set external sync info
            ExternalSource = SyncServiceEnum.NhanhVN,
            ExternalId = productData.ProductId.ToString(),

            // Set audit info
            Created = DateTime.Now,
            Updated = DateTime.Now
        };

        return standaloneItem;
    }

    /// <summary>
    /// Cập nhật existing standalone product từ dữ liệu Nhanh
    /// </summary>
    private void UpdateExistingStandaloneProduct(Items existingProduct, NhanhProductWebhookDto productData, string shopId, List<string> categoryIds)
    {
        // Cập nhật thông tin từ productData
        existingProduct.ItemsName = productData.Name;
        existingProduct.ItemsInfo = productData.Description;
        existingProduct.Price = (long?)productData.Price;
        existingProduct.Status = productData.Status == "Inactive" ? TypeStatus.InActived : TypeStatus.Actived;
        existingProduct.CategoryIds = categoryIds;

        // Cập nhật Images và VariantImage
        existingProduct.Images = CreateImageListFromProductData(productData);
        existingProduct.VariantImage = CreateVariantImageFromProductData(productData);

        // Cập nhật audit info
        existingProduct.Updated = DateTime.Now;
    }

    /// <summary>
    /// Tạo danh sách images từ product data
    /// </summary>
    private List<MediaInfo> CreateImageListFromProductData(NhanhProductWebhookDto productData)
    {
        var imageList = new List<MediaInfo>();

        // Thêm ảnh chính nếu có
        if (!string.IsNullOrEmpty(productData.Image))
        {
            imageList.Add(new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = productData.Image
            });
        }

        // Thêm các ảnh khác nếu có
        if (productData.Images != null && productData.Images.Count > 0)
        {
            foreach (var img in productData.Images)
            {
                if (!string.IsNullOrEmpty(img))
                {
                    imageList.Add(new MediaInfo
                    {
                        MediaFileId = Guid.NewGuid().ToString(),
                        Type = TypeMedia.IMAGE,
                        Link = img
                    });
                }
            }
        }

        return imageList;
    }

    /// <summary>
    /// Tạo VariantImage từ product data
    /// </summary>
    private MediaInfo CreateVariantImageFromProductData(NhanhProductWebhookDto productData)
    {
        // Ưu tiên ảnh chính, nếu không có thì lấy ảnh đầu tiên trong danh sách
        var imageUrl = !string.IsNullOrEmpty(productData.Image)
            ? productData.Image
            : productData.Images?.FirstOrDefault();

        if (!string.IsNullOrEmpty(imageUrl))
        {
            return new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = imageUrl
            };
        }

        return null;
    }

    /// <summary>
    /// Map carrier name/code từ Nhanh.vn sang TypeTransportService
    /// </summary>
    /// <param name="carrierName">Tên hãng vận chuyển từ Nhanh.vn</param>
    /// <param name="carrierCode">Mã hãng vận chuyển từ Nhanh.vn</param>
    /// <returns>TypeTransportService tương ứng hoặc null nếu không map được</returns>
    private static TypeTransportService? MapCarrierToTransportService(string? carrierName)
    {
        if (string.IsNullOrEmpty(carrierName))
            return null;

        // Map theo carrier name (ưu tiên)
        if (!string.IsNullOrEmpty(carrierName))
        {
            var normalizedName = carrierName.Trim().ToLowerInvariant();

            return normalizedName switch
            {
                var name when name.Contains("ahamove") => TypeTransportService.AHAMOVE,
                var name when name.Contains("j&t") || name.Contains("jt express") || name.Contains("jtexpress") => TypeTransportService.JTEXPRESS,
                var name when name.Contains("viettel") || name.Contains("vtp") => TypeTransportService.VTK,
                var name when name.Contains("vnpost") || name.Contains("vietnam post") => TypeTransportService.VCN,
                var name when name.Contains("ghn") || name.Contains("giao hàng nhanh") || name.Contains("giaohangnhanh") => TypeTransportService.NCOD,
                var name when name.Contains("ghtk") || name.Contains("giao hàng tiết kiệm") => TypeTransportService.LCOD,
                var name when name.Contains("viettel cargo") => TypeTransportService.VHT,
                var name when name.Contains("ems") => TypeTransportService.EMS,
                var name when name.Contains("ninjavan") || name.Contains("ninja van") => TypeTransportService.NINJAVAN,
                var name when name.Contains("best express") || name.Contains("bestexpress") => TypeTransportService.BESTEXPRESS,
                var name when name.Contains("post") => TypeTransportService.PTN,
                var name when name.Contains("phs") => TypeTransportService.PHS,
                _ => null
            };
        }

        return null;
    }

    /// <summary>
    /// Lấy danh sách đơn hàng từ Nhanh.vn API
    /// </summary>
    /// <param name="shopId">ID của shop</param>
    /// <param name="page">Trang hiện tại (mặc định: 1)</param>
    /// <returns>Response chứa danh sách đơn hàng và thông tin phân trang từ Nhanh.vn</returns>
    public async Task<Result<NhanhOrderSearchResponse>> GetNhanhOrdersAsync(string shopId, int page)
    {
        try
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.NhanhVN);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<NhanhOrderSearchResponse>.Failure("SYNC_NHANH_CONFIG_NOT_FOUND");

            using var httpClient = new HttpClient();
            var url = NhanhConstants.ApiOrder;

            var form = new MultipartFormDataContent
            {
                { new StringContent("2.0"), "version" },
                { new StringContent(config.AppId), "appId" },
                { new StringContent(config.BusinessId.ToString()), "businessId" },
                { new StringContent(config.AccessToken), "accessToken" },
                { new StringContent(JsonConvert.SerializeObject(new { page = page })), "data" }
            };

            var response = await httpClient.PostAsync(url, form);
            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();

            // Sử dụng JsonSerializerSettings để ignore errors khi deserialize
            var settings = new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                Error = (sender, args) =>
                {
                    args.ErrorContext.Handled = true;
                }
            };

            var apiResponse = JsonConvert.DeserializeObject<NhanhApiResponse<NhanhOrderSearchResponse>>(json, settings);

            if (apiResponse.Code != 1)
                return Result<NhanhOrderSearchResponse>.Failure("SYNC_NHANH_ORDER_API_ERROR");

            if (apiResponse.Data == null)
                return Result<NhanhOrderSearchResponse>.Success(new NhanhOrderSearchResponse
                {
                    Page = page,
                    TotalPages = 0,
                    TotalRecords = 0,
                    Orders = new Dictionary<string, NhanhOrderDetailDto>()
                });

            // Debug: Log thông tin pagination
            Console.WriteLine($"API Response - Page: {apiResponse.Data.Page}, TotalPages: {apiResponse.Data.TotalPages}, Orders Count: {apiResponse.Data.Orders?.Count}");

            return Result<NhanhOrderSearchResponse>.Success(apiResponse.Data);
        }
        catch (Exception ex)
        {
            return Result<NhanhOrderSearchResponse>.Failure("SYNC_NHANH_ORDER_API_EXCEPTION");
        }
    }

    /// <summary>
    /// Cập nhật existing variant item từ dữ liệu Nhanh
    /// </summary>
    private void UpdateExistingVariantItem(Items existingVariant, NhanhProductWebhookDto variantData, NhanhProductDetailDto parentProduct, List<NhanhProductDetailDto> allVariantsOfParent, string shopId, List<string> categoryIds)
    {
        var shop = _shopRepository.FindByShopId(shopId);

        // Lấy WarehouseId đầu tiên của shop
        var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
            .Result?.FirstOrDefault();
        var warehouseId = warehouse?.WarehouseId;

        // Cập nhật thông tin từ variantData và parentProduct
        existingVariant.ItemsName = parentProduct.Name; // Lấy tên từ sản phẩm cha
        existingVariant.ItemsInfo = variantData.Description;

        // Cập nhật Images và VariantImage
        var allImages = GetAllImagesFromVariants(allVariantsOfParent);
        existingVariant.Images = allImages;

        var variantImage = GetVariantImage(variantData, allVariantsOfParent);
        existingVariant.VariantImage = variantImage;

        existingVariant.Price = (long?)variantData.Price;
        existingVariant.PriceReal = (long?)variantData.Price;
        existingVariant.PriceCapital = (long?)variantData.Price;
        existingVariant.Quantity = variantData.Inventory?.Available ?? 0;
        existingVariant.ItemsHeight = variantData.Height;
        existingVariant.ItemsWidth = variantData.Width;
        existingVariant.ItemsLength = variantData.Length;
        existingVariant.ItemsWeight = variantData.Weight;
        existingVariant.CustomTaxRate = variantData.Vat;
        existingVariant.Status = variantData.Status == "Inactive" ? TypeStatus.InActived : TypeStatus.Actived;
        existingVariant.WarehouseId = warehouseId;
        existingVariant.CategoryIds = categoryIds;
        existingVariant.IsVariant = true;
        existingVariant.Updated = DateTime.UtcNow;

        // Set variant properties từ attributes
        if (variantData.Attributes != null && variantData.Attributes.Count > 0)
        {
            existingVariant.VariantNameOne = GetAttributeNameByIndex(variantData.Attributes, 0);
            existingVariant.VariantValueOne = GetAttributeValueByIndex(variantData.Attributes, 0);
            existingVariant.VariantNameTwo = GetAttributeNameByIndex(variantData.Attributes, 1);
            existingVariant.VariantValueTwo = GetAttributeValueByIndex(variantData.Attributes, 1);
            existingVariant.VariantNameThree = GetAttributeNameByIndex(variantData.Attributes, 2);
            existingVariant.VariantValueThree = GetAttributeValueByIndex(variantData.Attributes, 2);
        }

        // Set images từ variantData
        var imageList = new List<MediaInfo>();
        if (!string.IsNullOrEmpty(variantData.Image))
        {
            imageList.Add(new MediaInfo
            {
                MediaFileId = Guid.NewGuid().ToString(),
                Type = TypeMedia.IMAGE,
                Link = variantData.Image
            });
        }
        existingVariant.Images = imageList;
    }

}